name: Deploy

on:
  push:
    branches: [ dev, qa, staging, master ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Compile CSS/JS
        working-directory: ./src
        run: |
          npm install --quiet --force
          npm run build

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: mbstring, ctype, fileinfo, openssl, PDO, bcmath, json, tokenizer, xml, redis, mysql

      - name: Composer
        working-directory: ./src
        run: |
          composer install --no-dev --no-interaction --prefer-dist

      - name: Extract .env
        working-directory: ./src
        run: |
          if [[ $GITHUB_REF_NAME == "master" ]]; then
            ENV="production"
          else
            ENV=$GITHUB_REF_NAME
          fi
          php artisan env:decrypt --env=${ENV} --key=${{ secrets.ENV_KEY }}
          cp .env.${ENV} .env
          rm -rf .env.${ENV}

      - name: Create deploy artifact
        run: tar -czf ${{ github.sha }}.tar.gz --exclude=*.git --exclude=node_modules --exclude=tests src

      - name: Store artifact for distribution
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: ${{ github.sha }}.tar.gz
          if-no-files-found: error

  upload:
    name: Upload Build to Server
    runs-on: ubuntu-latest
    needs: build
    environment: ${{ github.ref_name }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: build
          path: ${{ github.sha }}

      - name: Upload
        uses: appleboy/scp-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          source: ${{ github.sha }}/*
          target: ${{ vars.SSH_PATH }}/versions

  prepare-release:
    name: Prepare release
    runs-on: ubuntu-latest
    needs: upload
    environment: ${{ github.ref_name }}
    steps:
      - name: Unpack release
        uses: appleboy/ssh-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          script_stop: true
          script: |
            cd ${{ vars.SSH_PATH }}/versions/${{ github.sha }}
            tar -xzf ${{ github.sha }}.tar.gz

  run-commands:
    name: Run pre-release commands
    runs-on: ubuntu-latest
    needs: prepare-release
    environment: ${{ github.ref_name }}
    steps:
      - name: Run commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          script_stop: true
          script: |
            cd ${{ vars.SSH_PATH }}/versions/${{ github.sha }}/src
            touch storage/logs/laravel.log
            php artisan migrate --force
            php artisan queue:restart
            php artisan clear-compiled
            php artisan cache:clear
            php artisan optimize

      - name: Fix permissions
        uses: appleboy/ssh-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          script_stop: true
          script: |
            cd ${{ vars.SSH_PATH }}/versions/${{ github.sha }}
            chown -R www-data:www-data src
            find src -type f -exec chmod 644 {} \;
            find src -type d -exec chmod 775 {} \;

  update-current-version:
    name: Point to current version
    runs-on: ubuntu-latest
    needs: run-commands
    environment: ${{ github.ref_name }}
    steps:
      - name: Update current version
        uses: appleboy/ssh-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          script_stop: true
          script: ln -sfn ${{ vars.SSH_PATH }}/versions/${{ github.sha }}/src ${{ vars.SSH_PATH }}/current

  cleanup:
    name: Cleanup older version
    runs-on: ubuntu-latest
    needs: update-current-version
    environment: ${{ github.ref_name }}
    steps:
      - name: Remove older versions
        uses: appleboy/ssh-action@master
        with:
          host: ${{ vars.SSH_HOST }}
          username: ${{ vars.SSH_USERNAME }}
          key: ${{ secrets.SSH_KEY_ED }}
          port: ${{ vars.PORT }}
          script: |
            cd ${{ vars.SSH_PATH }}/versions/
            ls -dt */ | tail -n +11 | xargs rm -rf
