name: Run tests

on:
  push:
    branches: [ dev, qa, staging, master, jogosho<PERSON> ]

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: mbstring, ctype, fileinfo, openssl, PDO, bcmath, json, tokenizer, xml, redis, mysql

      - name: Run composer install
        working-directory: ./src
        run: composer install -n --prefer-dist

      - name: Prepare Laravel Application
        working-directory: ./src
        run: |
          cp .env.ci .env
          php artisan key:generate

      - name: Run tests
        working-directory: ./src
        run: php artisan test --testsuite jogoshoje
