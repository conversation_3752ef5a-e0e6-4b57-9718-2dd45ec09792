<?php

use App\Http\Controllers\Api\Auth\{VerificationNoticeController, AuthCheckController, AuthProviderController, ForgotPasswordController, LoginController, LogoutController, ResetPasswordController, SignUpController, VerifyEmailController, DeleteUserController, MobileUsersAuthController};
use App\Http\Controllers\JogosHoje\Api\V3\{BalloonsController, CountriesController, FixturesBetometer1X2Controller, FixturesBetometerCardsController, FixturesBetometerController, FixturesBetometerCornersController, FixturesBetometerGoalsController, FixturesChatController, FixturesController, FixturesGameController, FixturesLineupsController, HomeController, LeaguesCompetitionInfoController, LeaguesController, LeaguesMatchesController, LeaguesPlayersController, LeaguesStandingsController, LeaguesStatsController, LeaguesStreaksController, LeaguesTeamOfTheWeekController, LeaguesTeamsController, OnboardingController, PagesController, PostController, PushNotificationsController, SearchController, TeamsCardsController, TeamsController, TeamsCornersController, TeamsFeaturedMatchController, TeamsGoalsController, TeamsMatchesController, TeamsPerformanceController, TeamsPlayersController, TeamsStandingsController, TheSportsProxyController, TrendsDiscoveryController, UpdateProfileController, UserFavoritesController, UsersMobileController};
use App\Http\Controllers\JogosHoje\{BotMakerController, SendEmailController};
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => 'web'], function () {
    $host = config('app.env') === 'local' ? 'api_jh.localhost' : config('cms.jogoshoje.api_domain');

    Route::group(['middleware' => 'auth'], function () {
        Route::prefix('auth')->group(function () {
            Route::get('/', AuthCheckController::class)->name('auth')->middleware('auth:sanctum');
            Route::post('signup', SignUpController::class)->name('auth.signup');
            Route::post('login', LoginController::class)->name('auth.login');
            Route::post('forgot-password', ForgotPasswordController::class)->name('auth.forgot-password');
            Route::post('reset-password', ResetPasswordController::class)->name('auth.reset-password');
            Route::post('verification-notice', VerificationNoticeController::class)
                ->name('auth.verification-notice')
                ->middleware('auth:sanctum');
            Route::post('logout', LogoutController::class)
                ->name('auth.logout')
                ->middleware('auth:sanctum');
            Route::delete('user', DeleteUserController::class)
                ->name('user.delete')
                ->middleware('auth:sanctum');
            Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
                ->name('auth.verify-email')
                ->middleware('auth:sanctum');
            Route::get('{provider}/redirect', [AuthProviderController::class, 'redirect'])->name(
                'auth.provider.redirect'
            )->whereIn('provider', AuthProviderController::PROVIDER_SUPPORTS);
            Route::get('{provider}/callback', [AuthProviderController::class, 'callback'])
                ->name('auth.provider.callback')
                ->whereIn('provider', AuthProviderController::PROVIDER_SUPPORTS);

            Route::get('user/favorites', [UserFavoritesController::class, 'show'])->name('user.favorites.show');
            Route::post('user/favorites', [UserFavoritesController::class, 'store'])->name('user.favorites.create');
            Route::delete('user/favorites', [UserFavoritesController::class, 'delete'])->name('user.favorites.delete');

            Route::post('user', UpdateProfileController::class)->name('profile')->middleware('auth:sanctum');
            Route::post('user-info', MobileUsersAuthController::class)->name('user.info');
        });
    });

    Route::domain($host)->middleware('json')->group(function () {
        Route::prefix('v2')->group(function () {
            Route::get(
                '/{param1?}/{param2?}/{param3?}/{param4?}',
                [\App\Http\Controllers\JogosHoje\Api\HomeController::class, 'index']
            )->name('jh_api');
            Route::post('/sendEmail', [SendEmailController::class, 'index']);
        });

        Route::prefix('v3')->group(function () {
            Route::get('/', HomeController::class)->name('home');
            Route::get('/search', SearchController::class)->name('search');

            Route::prefix('/trends-discovery')->group(function () {
                Route::get('/', [TrendsDiscoveryController::class, 'index'])->name('trendsDiscovery.index');
                Route::get('/{id}', [TrendsDiscoveryController::class, 'show'])->name('trendsDiscovery.show');
            });

            Route::prefix('/balloons')->group(function () {
                Route::get('/', BalloonsController::class)->name('balloons');
            });

            Route::prefix('/proxy')->group(function () {
                Route::get('/', TheSportsProxyController::class)->name('proxy');
            });

            Route::prefix('/fixtures')->group(function () {
                Route::get('/', [FixturesController::class, 'index'])->name('fixtures.index');
                Route::prefix('/{fixture}')->group(function () {
                    Route::get('/', [FixturesController::class, 'show'])->name('fixtures.show');
                    Route::get('/game', FixturesGameController::class)->name('fixtures.game');
                    Route::get('/chat', FixturesChatController::class)->name('fixtures.chat');
                    Route::prefix('/betometer')->group(function () {
                        Route::get('/', [FixturesBetometerController::class, 'index'])->name('fixtures.betometer');
                        Route::get('/1x2', FixturesBetometer1X2Controller::class)->name('fixtures.betometer.1x2');
                        Route::get('/goals', FixturesBetometerGoalsController::class)->name('fixtures.betometer.goals');
                        Route::get('/cards', FixturesBetometerCardsController::class)->name('fixtures.betometer.cards');
                        Route::get('/corners', FixturesBetometerCornersController::class)->name('fixtures.betometer.corners');
                    });
                    Route::get('/lineups', FixturesLineupsController::class)->name('fixtures.lineups');
                });
            });

            Route::prefix('/teams')->group(function () {
                Route::get('/', [TeamsController::class, 'index'])->name('teams.index');
                Route::prefix('/{team}')->group(function () {
                    Route::get('/', [TeamsController::class, 'show'])->name('teams.show');
                    Route::get('/performance', TeamsPerformanceController::class)->name('teams.performance');
                    Route::get('/goals', TeamsGoalsController::class)->name('teams.goals');
                    Route::get('/corners', TeamsCornersController::class)->name('teams.corners');
                    Route::get('/cards', TeamsCardsController::class)->name('teams.cards');
                    Route::get('/standings', TeamsStandingsController::class)->name('teams.standings');
                    Route::get('/matches', TeamsMatchesController::class)->name('teams.matches');
                    Route::get('/featured-match', TeamsFeaturedMatchController::class)->name('teams.featuredMatch');
                    Route::get('/players', TeamsPlayersController::class)->name('teams.players');
                });
            });

            Route::prefix('/leagues')->group(function () {
                Route::get('/', [LeaguesController::class, 'index'])->name('leagues.index');
                Route::prefix('/{league}')->group(function () {
                    Route::get('/', [LeaguesController::class, 'show'])->name('leagues.show');
                    Route::get('/competition-info', LeaguesCompetitionInfoController::class)->name('leagues.competition-info');
                    Route::get('/teams', LeaguesTeamsController::class)->name('leagues.teams');
                    Route::get('/players', LeaguesPlayersController::class)->name('leagues.players');
                    Route::get('/matches', LeaguesMatchesController::class)->name('leagues.matches');
                    Route::prefix('/streaks')->group(function () {
                        Route::get('/', [LeaguesStreaksController::class, 'index'])->name('leagues.streaks.index');
                        Route::get('/{teamId}', [LeaguesStreaksController::class, 'show'])->name('leagues.streaks.show');
                    });
                    Route::get('/team-of-the-week', LeaguesTeamOfTheWeekController::class)->name('leagues.team-of-the-week');
                    Route::get('/standings', LeaguesStandingsController::class)->name('leagues.standings');
                    Route::get('/stats', LeaguesStatsController::class)->name('leagues.stats');
                });
            });

            Route::prefix('/countries')->group(function () {
                Route::get('/', [CountriesController::class, 'index']);
                Route::prefix('/{country}')->group(function () {
                    Route::get('/', [CountriesController::class, 'show']);
                });
            });

            Route::prefix('/pages')->group(function () {
                Route::get('/', [PagesController::class, 'index']);
                Route::prefix('/{slug_url}')->group(function () {
                    Route::get('/', [PagesController::class, 'show']);
                });
            });

            Route::prefix('/notifications')->group(function () {
                Route::post('/', PushNotificationsController::class);
                Route::prefix('/users')->group(function () {
                    Route::post('/', UsersMobileController::class);
                });
            });

            Route::prefix('/onboarding')->group(function () {
                Route::get('/', OnboardingController::class);
            });

            Route::post('/contact', PostController::class)->name('contact');
        });

        Route::prefix('bot')->group(function () {
            Route::get(
                '/team/{country_slug}/{team_slug}/{status_slug}',
                [BotMakerController::class, 'getFixturesByCountryTeam']
            );
            Route::get('/notification/{customer_id}', [BotMakerController::class, 'show']);
            Route::post('/notification/store', [BotMakerController::class, 'store']);
            Route::delete('/notification/delete/{id}', [BotMakerController::class, 'destroy']);
            Route::delete('/notification/delete/all/{customer_id}', [BotMakerController::class, 'destroyBatch']);
        });
    });
});
