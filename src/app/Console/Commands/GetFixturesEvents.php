<?php

namespace App\Console\Commands;

use App\Helpers\Utils;
use App\Services\JogosHoje\FixtureEventService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Notifications\CommandErrorNotification;
use Illuminate\Support\Facades\Notification;

class GetFixturesEvents extends Command
{
    protected $signature = 'get:fixtures-events {type=live : Request type}';
    protected $description = 'Command to get fixtures-events';
    private const string NOTIFICATION_EMAIL = '<EMAIL>';


    public function __construct(private readonly FixtureEventService $service)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        Validator::validate($this->arguments(), [
            'type' => 'required',
        ]);

        try {
            $this->service->handle($this->argument('type'));
            return self::SUCCESS;
        } catch (\Exception $e) {
            $errorMessage = Utils::parseExceptionErrorMessage($e);
            $this->error($errorMessage);
            $this->sendErrorEmail('get:fixtures type=' . $this->argument('type'), $e->getMessage(), $e->getTraceAsString());
            return self::FAILURE;
        }
    }

    private function sendErrorEmail(string $commandName, string $errorMessage, string $stackTrace): void
    {
        try {
            Notification::route('mail', self::NOTIFICATION_EMAIL)
                ->notify(new CommandErrorNotification($commandName, $errorMessage, $stackTrace));
            $this->info("Error notification sent to: " . self::NOTIFICATION_EMAIL);
        } catch (\Exception $e) {
            $this->error("Failed to send error notification: " . $e->getMessage());
            Log::error("Failed to send error email", [
                'error' => $e->getMessage()
            ]);
        }
    }
}
