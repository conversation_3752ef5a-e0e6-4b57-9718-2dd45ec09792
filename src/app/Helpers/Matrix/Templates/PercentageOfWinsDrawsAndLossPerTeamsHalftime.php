<?php
namespace App\Helpers\Matrix\Templates;

use \Illuminate\Support\Collection;

class PercentageOfWinsDrawsAndLossPerTeamsHalftime extends BaseTemplate
{
    public const TABLE = [
        ['games' => 0, 'playing_home' => 1],
        ['games' => 0, 'playing_home' => 0],
    ];

    public static function matrix(array $rows, Collection $collection, array $extraData): array
    {
        $data = [];
        $table = self::TABLE;

        foreach ($table as $index => &$item) {
            $fixtures = self::filterFixtures(
                $collection,
                $item['playing_home']
            );

            $newRows = [];
            foreach ($rows as $row) {
                $newRow = $row;
                $value = self::calculatePercentage(
                    $fixtures,
                    $row
                );

                $newRow['percentage'] = $value['percentage'];
                $newRows []= $newRow;
            }

            $item['title'] = $extraData['teams'][$index]->name;
            $item['games'] = $value['games'];
            $item['rows'] = $newRows;

            $data[] = $item;
        }

        return $data;
    }

    private static function filterFixtures(Collection $collection, int $playingHome): Collection
    {
        return $collection->filter(fn($c) => $c->playing_home == $playingHome);
    }

    public static function calculatePercentage(Collection $fixtures, array $row): array
    {
        return self::calculateWinsDrawsAndLossPercentage($fixtures, $row);
    }
}