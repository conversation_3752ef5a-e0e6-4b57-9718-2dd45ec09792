<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        // AL
        'https://api.apostalegal.com/*',
        'https://api.apostalegal.com/ajax/*',
        'https://staging-api.apostalegal.com/*',
        'https://staging-api.apostalegal.com/ajax/*',
        'https://dev-api.apostalegal.com/*',
        'https://dev-api.apostalegal.com/ajax/*',
        'https://qa-api.apostalegal.com/*',
        'https://qa-api.apostalegal.com/ajax/*',

        'http://api.localhost:888/*',
        'http://api.localhost:888/ajax/*',
        'http://api_jh.localhost:888/bot/*',

        'http://api.localhost/*',
        'http://api.localhost/ajax/*',
        'http://api_jh.localhost/bot/*',

        // JogosHoje
        'https://api.jogoshoje.com/*',
        'https://api.jogoshoje.com/general/sendEmail',
        'https://api.jogoshoje.com/bot/*',
        'https://staging-api.jogoshoje.com/*',
        'https://staging-api.jogoshoje.com/bot/*',
        'https://dev-api.jogoshoje.com/*',
        'https://dev-api.jogoshoje.com/bot/*',
        'https://qa-api.jogoshoje.com/*',
        'https://qa-api.jogoshoje.com/bot/*',

        'http://api_jh.localhost/*',
        'https://jogoshoje-git-develop-altsdigital.vercel.app/*',
        'https://preview-api.jogoshoje.com/*',
        'https://cms.jogoshoje.com/*',
        'http://localhost/*',
    ];
}
