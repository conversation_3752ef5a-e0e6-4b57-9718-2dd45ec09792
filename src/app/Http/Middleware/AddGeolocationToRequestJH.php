<?php

namespace App\Http\Middleware;

use App\Models\JogosHoje\{Country, Region, TimezoneOffset};
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\{App, Cache, Date};

class AddGeolocationToRequestJH
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        $host = $request->headers->get('host');
        if ((str_contains($host, 'api_jh') || str_contains($host, 'jogoshoje.com'))) {
            $ip = $request->get('User-IP');

            $data = [
                'timezone' => 'UTC',
                'continentIsoCode' => 'EU', // SA
                //'countryIsoCode3' => 'PRT', // BRA
                'countryIsoCode2' => 'PT', // BR
                'region' => null,
                'country' => null,
                'offset_tz' => null,
            ];

            if ($ip) {
                $geo = Cache::remember(
                    config('app.env') . '-jh-' . $ip . '-ip',
                    Date::now()->addMonths(3),
                    fn() => geoip()->getLocation($ip)?->toArray()
                );

                if ($geo) {
                    $timezone = isset($geo['time_zone']) ? $geo['time_zone']['name'] : ($geo['timezone'] ?? $data['timezone']);

                    $data['timezone'] = $request->get('timezone', $timezone);
                    if (isset($geo['country_code3'])) {
                        $data['countryIsoCode3'] = $geo['country_code3'];
                    }
                    if (isset($geo['iso_code'])) {
                        $data['countryIsoCode2'] = $geo['iso_code'];
                    }

                    $data['country'] = Cache::rememberForever(
                        'country_' . ($data['countryIsoCode3'] ?? (isset($data['countryIsoCode2']) ? $data['countryIsoCode2'] : '')),
                        fn() => Country::getByCode($data)
                    );

                    if (isset($geo['continent_code']) || isset($geo['continent'])) {
                        $data['continentIsoCode'] = $geo['continent_code'] ?? $geo['continent'];

                        $data['region'] = Cache::rememberForever(
                            'region_' . $data['continentIsoCode'],
                            fn() => Region::getByCode($data['continentIsoCode'])
                        );
                    }
                }
            }

            $data['offset_tz'] = Cache::rememberForever(
                'offset_' . $data['timezone'],
                static function () use ($data) {
                    $offset = Carbon::now($data['timezone'])->format('P');
                    return TimezoneOffset::query()->where('offset', $offset)->first();
                }
            );

            $this->setAppLocaleByUserCountry($data['countryIsoCode2']);

            $request->merge($data);
        }

        return $next($request);
    }

    private function setAppLocaleByUserCountry(string $countryIsoCode): void
    {
        $locale = $this->getCountryLocale($countryIsoCode);
        App::setLocale($locale);
    }

    private function getCountryLocale(string $countryIsoCode): string
    {
        return match ($countryIsoCode) {
            'BR' => 'pt_BR',
            'PT' => 'pt_PT',
            default => 'en_US',
        };
    }
}
