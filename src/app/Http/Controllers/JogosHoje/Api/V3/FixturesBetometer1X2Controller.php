<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\TeamResource;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\FixtureBetometer1x2Service;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class FixturesBetometer1X2Controller extends Controller
{
    public function __construct(
        private readonly FixtureBetometer1x2Service $service,
    ) {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $leagueId = $request->get('league_id', $fixture->league_id);
        $section = $request->get('section');
        $teamId = $request->get('team_id');

        $cacheKey = "fixturesBetometer1X2-{$fixture->id}-" . join('-', [$leagueId, $section, $teamId, $request->timezone, $request->get('page')]);
        $tagsKey = "1X2-{$fixture->id}";

        return Cache::tags([$tagsKey])->remember(
            $cacheKey,
            Date::now()->addMinutes($fixture->cacheTtl()),
            fn() => response()->json(match ($section) {
                'streaks' => $this->service->getStreaks($fixture, $request),
                'form' => [
                    'fixtures' => $this->service->getH2H(
                        $request,
                        $fixture,
                        $teamId,
                        $leagueId
                    ),
                    'home_team' => new TeamResource($fixture->homeTeam),
                    'away_team' => new TeamResource($fixture->awayTeam)
                ],
                '1X2HT' => $this->service->matrixOf1x2HTForFixtureTeams($fixture, $leagueId),
                'predictions' => $this->service->predictions($fixture),
                default => null
            })
        );
    }
}
