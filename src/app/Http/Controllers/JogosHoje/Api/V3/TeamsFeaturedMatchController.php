<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\FixtureResource;
use App\Models\JogosHoje\Team;
use App\Services\JogosHoje\TeamService;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class TeamsFeaturedMatchController extends Controller
{
    public function __construct(
        private readonly TeamService $service
    ) {
    }

    public function __invoke(Request $request, Team $team): FixtureResource|JsonResponse
    {
        return Cache::remember(
            'teams-featured-match-' . $team->id,
            Date::now()->addDay(),
            function () use ($team) {
                //$query = $this->service->featuredMatch($team->id);

                //if (!$query) {
                    return response()->json(null);
                //}

                //return new FixtureResource($query);
            }
        );
    }
}
