<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\League;
use App\Services\JogosHoje\{FixtureService, LeagueRoundsService, LeagueService, StandingService};
use App\Traits\FixturesTrait;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class LeaguesStandingsController extends Controller
{
    use FixturesTrait;

    public function __construct(
        private readonly LeagueService $service,
        private readonly LeagueRoundsService $leagueRoundsService,
        private readonly FixtureService $fixtureService,
        private readonly StandingService $standingService,
    ) {
    }

    public function __invoke(Request $request, League $league): JsonResponse
    {
        $seasonId = $request->get('season_id');
        $round = $request->get('round');

        Cache::forget('leagues-standings-' . $league->id . '-' . join('-', [$seasonId, $round]));

        return Cache::remember(
            'leagues-standings-' . $league->id . '-' . join('-', [$seasonId, $round]),
            Date::now()->addHour(),
            function () use ($league, $request) {
                $rounds = $this // league rounds
                    ->leagueRoundsService
                    ->getLeagueRounds($league->id)
                    ->get();

                $leagueRounds = $this->leagueRoundsService->differentLeagueRounds($rounds);

                $fixtureLastRound = $this->fixtureService->getLastRound($league->id);

                $json = [
                    'qualification' => $this->standingService->getQualificationData($leagueRounds, $league, $request, $fixtureLastRound),
                    'standings' => $this->standingService->getStandingsData($leagueRounds, $league, $request, $fixtureLastRound),
                    'brackets' => $this->standingService->getBracketsData($leagueRounds, $league, $request, $fixtureLastRound),
                ];

                return response()->json($json);
            }
        );
    }
}
