<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\{FixtureResource, RefereeResource, VenueResource};
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\{FixtureGameService, StandingService, LeagueRoundsService, FixtureService};
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class FixturesGameController extends Controller
{
    public function __construct(
        private readonly FixtureGameService $service,
        private readonly StandingService $standingService,
        private readonly LeagueRoundsService $leagueRoundsService,
        private readonly FixtureService $fixtureService,
    ) {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $leagueId = $request->get('league_id', $fixture->league_id);

        return Cache::remember(
            'fixturesGame-' . $fixture->id . '-' . join('-', [$leagueId, $request->timezone]),
            Date::now()->addMinutes($fixture->cacheTtl()),
            function () use ($fixture, $leagueId, $request) {
                $data = [
                    'statistics' => null,
                    'events' => null,
                ];

                $data['form'] = $this->service->form($fixture, $leagueId);

                $rounds = $this
                    ->leagueRoundsService
                    ->getLeagueRounds($leagueId)
                    ->get();

                $leagueRounds = $this->leagueRoundsService->differentLeagueRounds($rounds);

                $fixtureLastRound = $this->fixtureService->getLastRound($leagueId);

                $data['standings'] = [
                    'qualification' => $this->standingService->getQualificationData($leagueRounds, $fixture->league, $request, $fixtureLastRound),
                    'standings' => $this->standingService->getStandingsData($leagueRounds, $fixture->league, $request, $fixtureLastRound),
                    'brackets' => $this->standingService->getBracketsData($leagueRounds, $fixture->league, $request, $fixtureLastRound),
                ];

                $data['match_info'] = [
                    'referee' => $fixture->referee ? [
                        'details' => new RefereeResource($fixture->referee),
                        'cards' => $this->service->refereeCards($fixture, $fixture->league_id, $fixture->date),
                    ] : null,
                    'venue' => $fixture->venue ? new VenueResource($fixture->venue) : null,
                    'broadcasters' => $fixture->broadcasters,
                ];

                if ($fixture::isLive($fixture->status) || $fixture::isFinished($fixture->status)) {
                    $data['statistics'] = $this->service->statistics($fixture);
                    $data['events'] = $this->service->events($fixture);
                }

                $data['fixture'] = new FixtureResource($fixture);

                return response()
                    ->json($data);
            }
        );
    }
}
