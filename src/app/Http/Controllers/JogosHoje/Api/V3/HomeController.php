<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\Fixture;
use App\Models\User;
use App\Services\JogosHoje\FavoriteService;
use Carbon\Carbon;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Crypt, Date};

class HomeController extends Controller
{
    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly FavoriteService $userFavoritesService,
        private readonly User $userModel
    ) {
    }

    public function __invoke(Request $request): JsonResponse
    {
        $tzOffset = Carbon::now($request->timezone)->format('P');
        $token = $request->header('Auth-token');

        $fixtures = Cache::remember(
            'homeController-' . $tzOffset . '-' . $token,
            Date::now()->addHour(),
            function () use ($tzOffset, $token) {
                if (!$token) {
                    return [];
                }

                $user = $this->userModel::find(Crypt::decryptString($token));
                $userFavorites = $this->userFavoritesService->userFavorites($user);

                return $this->fixtureModel
                    ->userFavoritefixtures($userFavorites, $tzOffset)
                    ->map(fn($item) => $item->date);
            }
        );

        $liveFixturesCount = Cache::remember(
            'homeControllerLiveCount',
            Date::now()->addMinute(),
            function () {
                return $this->fixtureModel
                    ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
                    ->where('fixtures.is_live', 1)
                    ->whereRaw('leagues.in_use = 1')
                    ->count();
            }
        );

        return response()
            ->json([
                'fixtures' => $fixtures,
                'live_fixtures_count' => $liveFixturesCount,
            ]);
    }
}
