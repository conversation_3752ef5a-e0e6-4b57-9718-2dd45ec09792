<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\FixtureBetometerCornersService;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};
class FixturesBetometerCornersController extends Controller
{
    public function __construct(
        private readonly FixtureBetometerCornersService $service
    ) {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $leagueId = $request->get('league_id', $fixture->league_id);
        $homeAway = $request->get('home_away', 0);
        $section = $request->get('section');

        $cacheKey = "fixturesBetometerCorners-{$fixture->id}-{$leagueId}-{$homeAway}-{$section}";
        $tagsKey = "Corners-{$fixture->id}";

        return Cache::tags([$tagsKey])->remember(
            $cacheKey,
            Date::now()->addMinutes($fixture->cacheTtl()),
            function () use ($fixture, $request, $leagueId, $homeAway, $section) {
                $json = match ($section) {
                    'streaks' => $this->service->getStreaks($fixture, $request),
                    'total' => $this->service->totalCorners($fixture, $leagueId, $homeAway),
                    'in_favor' => $this->service->inFavorCorners($fixture, $leagueId, $homeAway),
                    'against' => $this->service->againstCorners($fixture, $leagueId, $homeAway),
                    default => null,
                };

                if (in_array($section, ['total', 'in_favor', 'against'])) {
                    $json['hide_buttons'] = false;

                    $latestLeagueSeason = $fixture->getLatestFixtureNonCurrentLeagueSeason($leagueId);

                    if ($latestLeagueSeason->exists()) {
                        $seasonEndDate = $latestLeagueSeason->first()->end;

                        if (strtotime($fixture->date) > strtotime($seasonEndDate)) {
                            $json['hide_buttons'] = true;
                        }
                    }
                }

                return response()->json($json);
            }
        );
    }
}
