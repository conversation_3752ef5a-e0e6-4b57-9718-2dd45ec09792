<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\PlayerResource;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\{FixtureBetometerCardsService, FixtureGameService};
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class FixturesBetometerCardsController extends Controller
{
    public function __construct(
        private readonly FixtureBetometerCardsService $service,
        private readonly FixtureGameService $gameService
    ) {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $leagueId = $request->get('league_id', $fixture->league_id);
        $homeAway = $request->get('home_away', 0);
        $section = $request->get('section');

        $cacheKey = "fixturesBetometerCards-{$fixture->id}-{$leagueId}-{$homeAway}-{$section}";
        $tagsKey = "Cards-{$fixture->id}";

        return Cache::tags([$tagsKey])->remember(
            $cacheKey,
            Date::now()->addMinutes($fixture->cacheTtl()),
            fn() => response()->json(match ($section) {
                'streaks' => $this->service->getStreaks($fixture, $request),
                'total' => $this->service->totalCards($fixture, $leagueId, $homeAway),
                'in_favor' => $this->service->inFavorCards($fixture, $leagueId, $homeAway),
                'against' => $this->service->againstCards($fixture, $leagueId, $homeAway),
                'top_cards' => PlayerResource::collection($this->service->topCards($fixture, $leagueId, $homeAway)),
                'referee' => $this->service->referee($fixture, $leagueId, $this->gameService),
                default => null,
            })
        );
    }
}
