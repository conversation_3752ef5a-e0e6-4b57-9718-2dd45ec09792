<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\BaseController;
use App\Http\Resources\JogosHoje\FixtureResource;
use App\Models\JogosHoje\Fixture;
use App\Models\JogosHoje\FixtureDate;
use App\Models\JogosHoje\League;
use App\Models\User;
use App\Services\JogosHoje\FavoriteService;
use App\Services\JogosHoje\FixtureLineupService;
use App\Services\JogosHoje\FixturesDatesService;
use App\Traits\FixturesTrait;
use Carbon\Carbon;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Crypt, Date};

class FixturesController extends BaseController
{
    use FixturesTrait;

    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly FixtureLineupService $lineupService,
        private readonly FavoriteService $userFavoritesService,
        private readonly User $userModel,
        private readonly League $leagueModel,
        private readonly FixtureDate $fixtureDateModel,
        private readonly FixturesDatesService $fixturesDatesService,
    ) {
    }

    public function index(Request $request): JsonResponse
    {
        $now = Carbon::now()->timezone($request->timezone);
        $tzOffset = Carbon::now($request->timezone)->format('P');
        $page = $request->get('page', 1);
        $isLive = $request->get('is_live');
        $leagueId = $request->get('league_id');
        $token = $request->header('Auth-token');

        $date = match ($request->get('date')) {
            'yesterday' => $now->copy()->subDay()->format('Y-m-d'),
            'tomorrow' => $now->copy()->addDay()->format('Y-m-d'),
            default => ($request->get('date') ?? $now->format('Y-m-d')),
        };

        $ttl = ($date === $now->format('Y-m-d') || $request->get('is_live')) ? 30 : 300;

        // Get fixtures Ids for the given date and timezone
        $fixturesIds = $this->fixturesDatesService
            ->getFixturesIds($date, $request->offset_tz);

        // Get fixtures to create pagination
        $fixtures = $this->fixtureModel->getFixturesLimitsAndOffsetsForADay($request, $fixturesIds);

        // Generate pagination
        $pagination = $this->getDynamicPagination($fixtures);

        // Get next fixtures if no fixtures for the given date and a league is provided
        $nextFixtures = [];
        if ((sizeof($fixtures) === 0) && $leagueId) {
            $nextFixtures = Cache::remember(
                "fixturesUpcoming-{$leagueId}-{$request->timezone}-{$tzOffset}",
                Date::now()->addDay(),
                fn() => $this->parseFixtures(
                    $this->fixtureModel->upcomingFixtures($leagueId, $tzOffset, $date),
                    true,
                    $request
                )
            );
        }

        // Set offset and limit for the given page
        [$offset, $limit] = $pagination[$page];

        return Cache::remember(
            "fixturesIndex-{$date}-{$tzOffset}-{$isLive}-{$leagueId}-{$request->timezone}-{$token}-{$offset}-{$limit}",
            Date::now()->addSeconds($ttl),
            function () use ($request, $date, $tzOffset, $offset, $limit, $nextFixtures, $token, $fixturesIds, $fixtures) {
                $dayFixtures = $this->fixtureModel->getFixturesForADay(
                    $request,
                    $offset,
                    $limit,
                    $fixturesIds
                )->get();

                $dayFixtures->each(fn(&$fixture) => $this->lineupService->checkPreviousLineups($fixture));

                $userFavorites = collect([]);
                if ($token) {
                    $user = $this->userModel::find(Crypt::decryptString($token));
                    $userFavorites = $this->userFavoritesService->getUserFavoriteGames($request, $user, $fixturesIds);
                }

                return response()
                    ->json([
                        'fixtures' => $this->parseFixtures($dayFixtures, true, $request),
                        'total' => sizeof($fixtures),
                        'next_fixtures' => $nextFixtures,
                        'favorites' => $this->parseFixtures($userFavorites, true, $request),
                    ]);
            }
        );
    }

    public function show(Request $request, Fixture $fixture): FixtureResource
    {
        $ttl = $fixture->cacheTtl() == 1 ? Date::now()->addSeconds(30) : Date::now()->addMinutes($fixture->cacheTtl());

        return Cache::remember(
            'fixturesShow-' . $fixture->id . '-' . $request->timezone,
            $ttl,
            function () use ($fixture) {
                $this->lineupService->checkPreviousLineups($fixture);
                return new FixtureResource($fixture);
            }
        );
    }
}
