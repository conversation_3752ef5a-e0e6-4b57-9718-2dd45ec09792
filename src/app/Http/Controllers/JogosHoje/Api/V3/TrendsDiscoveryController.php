<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Enums\JogosHoje\StreakTranslation;
use App\Http\Controllers\Controller;
use App\Models\JogosHoje\{Statistic, StatisticDetails};
use App\Services\JogosHoje\ImagePathService;
use App\Traits\FixturesTrait;
use Carbon\Carbon;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class TrendsDiscoveryController extends Controller
{
    use FixturesTrait;

    public function __construct(
        private readonly Statistic $statisticModel,
        private readonly StatisticDetails $statisticDetailsModel,
    ) {
    }

    public function index(Request $request): JsonResponse
    {
        $section = $request->get('section');
        $offset = $request->get('offset', 0);

        $jsonData = match ($section) {
            'sidebar' => Cache::remember(
                'trendsdiscovery-sidebar',
            Date::now()->addDay(),
            function() {
                return $this->statisticModel
                    ->streaksTypes()
                    ->get()
                    ->map(function ($item) {
                        $item->streak = str_replace(['_over', '_under'], '', $item->name);
                        $item->name = StreakTranslation::getTranslationByKey(str_replace(['_over', '_under'],'', $item->name));
                        $item->thresholds = $this->handleThresholds(explode(',', $item->thresholds));

                        return $item;
                    })
                    ->filter(function ($item) {
                        return $item->name !== '';
                    })
                    ->groupBy('type')
                    ->toArray();
            }),
            'list' => Cache::remember(
                'trendsdiscovery-list-' . join('-', [$section, $offset, $request->timezone]),
                Date::now()->addHour(),
                function () use ($request, $offset) {
                    return $this->statisticModel
                        ->streaks(null, $offset)
                        ->get()
                        ->map(function ($item) use ($request) {
                            $item->date = Carbon::parse($item->fixture_date)->timezone($request->timezone)->diffForHumans();
                            $item->fixture_small_date = Carbon::parse($item->fixture_date)->timezone($request->timezone)->format('Y-m-d');
                            $item->fixture_date = Carbon::parse($item->fixture_date)->timezone($request->timezone)->format('Y-m-d H:i:s');
                            $item->media = ImagePathService::get($item);
                            $item->percentage = round($item->percentage, 2);
                            $item->count = (int) $item->count;

                            return $item;
                        })
                        ->sortBy([['percentage', 'desc'], ['count', 'desc']])
                        ->values();
                }
            ),
            default => [],
        };

        return response()->json($jsonData);
    }

    public function show(Request $request, $id): JsonResponse
    {
        return Cache::remember(
            'trendsdiscovery-' . $id . '-' . $request->timezone,
            Date::now()->addDay(),
            function () use ($id, $request) {
                $result = $this->statisticModel
                    ->with(['market', 'statisticDetailOne', 'statisticDetailTwo', 'fixture'])
                    ->find($id);

                $json = $this->parseStreakResult($result, $request);

                return response()
                    ->json($json);
            }
        );
    }

    private function handleThresholds(array $thresholds): array
    {
        $data = [];

        foreach ($thresholds as $threshold) {
            $parts = explode(' ', $threshold);
            $key = $parts[0];

            if (!isset($data[$key])) {
                $data[$key] = [];
            }

            if (count($parts) > 1) {
                $data[$key][] = $parts[1];
            }
        }

        return collect($data)->map(fn ($item) =>
            collect($item)->sort()->values()->toArray()
        )->toArray();
    }
}
