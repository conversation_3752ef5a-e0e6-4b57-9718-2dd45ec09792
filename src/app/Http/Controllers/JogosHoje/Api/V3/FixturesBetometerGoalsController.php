<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\PlayerResource;
use App\Models\JogosHoje\Fixture;
use App\Services\JogosHoje\FixtureBetometerGoalsService;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class FixturesBetometerGoalsController extends Controller
{
    public function __construct(
        private readonly FixtureBetometerGoalsService $service
    ) {
    }

    public function __invoke(Request $request, Fixture $fixture): JsonResponse
    {
        $leagueId = $request->get('league_id', $fixture->league_id);
        $homeAway = $request->get('home_away', 0);
        $section = $request->get('section');

        $cacheKey = "fixturesBetometerGoals-{$fixture->id}-{$leagueId}-{$homeAway}-{$section}";
        $tagsKey = "Goals-{$fixture->id}";

        return Cache::tags([$tagsKey])->remember(
            $cacheKey,
            Date::now()->addMinutes($fixture->cacheTtl()),
            fn() => response()->json(match ($section) {
                'streaks' => $this->service->getStreaks($fixture, $request),
                'total' => $this->service->totalGoals($fixture, $leagueId, $homeAway),
                'in_favor' => $this->service->inFavorGoals($fixture, $leagueId, $homeAway),
                'against' => $this->service->againstGoals($fixture, $leagueId, $homeAway),
                'btts' => $this->service->bttsGoals($fixture, $leagueId, $homeAway),
                'top_scorers' => PlayerResource::collection($this->service->scorers($fixture, $leagueId, $homeAway)),
                'who_will_score_first' => $this->service->whoWillScoreFirst($fixture, $leagueId, $homeAway),
                'goals_per_minute' => $this->service->goalsPerMinute($fixture, $leagueId, $homeAway),
                default => null,
            })
        );
    }
}
