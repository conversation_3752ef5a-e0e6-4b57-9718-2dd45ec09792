<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\JogosHoje\TeamService;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class TeamsMatchesController extends Controller
{
    public function __construct(
        private readonly TeamService $service
    ) {
    }

    public function __invoke(Request $request, int $teamId): JsonResponse
    {
        $offset = $request->get('offset');
        $limit = $request->get('limit');
        $leagueId = $request->get('league_id');

        return Cache::remember(
            'teams-matches-' . $teamId . '-' . implode('-', [$offset, $limit, $leagueId, $request->timezone]),
            Date::now()->addDay(),
            fn() => response()->json($this->service->matches($teamId, $request))
        );
    }
}
