<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\JogosHoje\StandingResource;
use App\Models\JogosHoje\League;
use App\Models\JogosHoje\Team;
use App\Services\JogosHoje\{FixtureService, LeagueRoundsService, LeagueTeamService, StandingService, TeamService};
use App\Traits\FixturesTrait;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Support\Facades\{Cache, Date};

class TeamsStandingsController extends Controller
{
    use FixturesTrait;

    public function __construct(
        private readonly TeamService $service,
        private readonly LeagueTeamService $leagueTeamService,
        private readonly StandingService $standingService,
        private readonly FixtureService $fixtureService,
        private readonly LeagueRoundsService $leagueRoundsService,
        private readonly League $leagueModel,
    ) {
    }

    public function __invoke(Request $request, Team $team): JsonResponse
    {
        $round = $request->get('round');
        $leagueId = $request->get('league_id');
        $seasonId = $request->get('season_id');

        Cache::forget('teams-standings-' . $team->id . '-' . join('-', [$round, $leagueId, $seasonId]));

        return Cache::remember(
            'teams-standings-' . $team->id . '-' . join('-', [$round, $leagueId, $seasonId]),
            Date::now()->addDay(),
            function () use ($team, $request, $round) {
                $standings = $this->service->standings($team->id, $request);
                
                $league = $this->leagueModel->find($standings['league_id']);                

                $rounds = $this // league rounds
                    ->leagueRoundsService
                    ->getLeagueRounds($league->id)
                    ->get();

                $leagueRounds = $this->leagueRoundsService->differentLeagueRounds($rounds);

                $leagueTeams = $this->leagueTeamService->getAllForATeam($team->id);

                $seasons = $leagueTeams
                    ->groupBy('season_id')
                    ->values()
                    ->map(function ($items) use ($standings) {
                        foreach ($items as &$item) {
                            $item->selected = $item->league_id == $standings['league_id'] && $item->season_id == $standings['season_id'];
                        }

                        return $items;
                    })
                    ->toArray();

                return response()->json([
                    'seasons' => $seasons,
                    'standings' => [
                        'qualification' => $this->standingService->getQualificationData($leagueRounds, $league, $request),
                        'standings' => $this->getStandingsData($standings, $seasons),
                        'brackets' => $this->standingService->getBracketsData($leagueRounds, $league, $request),
                    ],
                ]);
            }
        );
    }

    private function getStandingsData(array $standings, array $seasons): array
    {
        //dump($seasons);
        //dd($standings);
        $titles = $standings['standings']
            ->groupBy('group')
            ->keys()
            ->values()
            ->toArray();

        return [
            'titles' => $titles,
            'current' => null,
            'data' => [],
        ];
    }
}