<?php

namespace App\Http\Controllers\JogosHoje\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class TheSportsProxyController extends Controller
{
    public function __invoke(Request $request): string
    {
        $type = $request->get('type');
        $theSportsUUID = $request->get('the_sports_id');
        $language = $request->get('language', 'br');
        $timestamp = time();

        $liveTracker = <<<LIVE_TRACKER
            <iframe src="https://widgets.thesports01.com/{$language}/3d/football?profile=m8gh9lj28x6iku2&uuid={$theSportsUUID}" />
        LIVE_TRACKER;

        $gameMomentum = <<<GAME_MOMENTUM
        <script type="text/javascript">
            (function (w, s, p, o, r, t) {
            r = document.createElement(s);
            t = document.getElementsByTagName(s)[0];
            r.async = true;
            r.src = p;
            t.parentNode.insertBefore(r, t);
            r.onload = () => {
                w['thesports']?.setProfile(o.profile, o.options);
            };
            })(window, 'script', '//cdn-saas.thesports.com/loader.umd.js?t={$timestamp}', {
            profile: {
                profile_id: "jnh24z3xe0u8unsz",
                sport: "football",
                widget_id: "momentum",
                match_id: "{$theSportsUUID}",
                lang: "{$language}",
            },options: {
            theme: "dark",
            color_primary_light: "#FF0100",
            color_bg_light: "#FFFFFF",
            color_text_light: "#050917",
            color_primary_dark: "#A3A3A3",
            color_bg_dark: "#1C1D21",
            color_text_dark: "#55585E",
            }
        });
        </script>
        GAME_MOMENTUM;

        $result = match ($type) {
            'game-momentum' => $gameMomentum,
            'live-tracker' => $liveTracker,
            default => '',
        };

        return $result;
    }
}
