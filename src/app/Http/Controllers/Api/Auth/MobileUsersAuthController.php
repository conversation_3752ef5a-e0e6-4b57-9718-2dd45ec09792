<?php

namespace App\Http\Controllers\Api\Auth;

use App\Http\Controllers\BaseController;
use App\Http\Requests\MobileUserSignUpRequest;
use App\Models\JogosHoje\UserMobile;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\UserResource;
use Illuminate\Auth\Events\Registered;

class MobileUsersAuthController extends BaseController
{
    public const string REGISTERED = 'registered';
    public const string FAIL = 'fail';

    public function __invoke(MobileUserSignUpRequest $request): JsonResponse
    {
        try {
            $user = User::createUserForSite($request->validated(), currentSite());

            if ($request->get('token')) {
                $userMobile = UserMobile::firstOrNew(['user_id' => $user->id]);
                $userMobile->token = $request->get('token');
                $userMobile->device = $request->get('device');
                $userMobile->save();
            }

            if ($request->get('provider')) {
                $map = [
                    'google' => 'google_id',
                    'facebook' => 'facebook_id',
                    'apple' => 'apple_id',
                ];

                $providerKey = $map[$request->get('provider')];
                $user->$providerKey = $request->get('provider_id');
                $user->save();
            }

            /*
             * Custom email is sent instead of the built-in one.
             * @see \App\Providers\AuthServiceProvider::boot
             */
            if ($user->wasRecentlyCreated) {
                event(new Registered($user));
            }

            Auth::login($user);

            $status = self::REGISTERED;
            $data = UserResource::make($user);
        } catch (\Exception $e) {
            $status = self::FAIL;
            $data = ['error' => $e->getMessage()];
        }

        return response()->json([
            'status' => $status,
            'data' => $data
        ]);
    }
}
