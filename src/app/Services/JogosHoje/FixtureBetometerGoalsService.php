<?php

namespace App\Services\JogosHoje;

use App\Enums\JogosHoje\StatisticGoalType;
use App\Helpers\Matrix\Table;
use App\Helpers\Matrix\Templates\{BTTSFullTimeAndFirstSecondHalfPerTeams, GoalsPerMinuteScoredAndConcededPerTeams, GoalsTotalsFullTimeAndFirstSecondHalfTeams};
use App\Models\JogosHoje\{Fixture, FixtureEvent};
use App\Models\MediaSite;
use App\Traits\{FixturesTrait, JHCacheTrait};
use Illuminate\Http\Request;
use Illuminate\Support\Collection as SupportCollection;

class FixtureBetometerGoalsService
{
    use JHCacheTrait, FixturesTrait;

    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly FixtureEvent $fixtureEventModel,
        private readonly MediaSite $mediaSiteModel,
    ) {
    }

    public function totalGoals(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureModel
            ->maxGoalsForTeams($fixture->date, $leagueId);

        $json['avg'] = $this->fixtureModel
            ->avgGoalsForTeams($fixture->team_home, $fixture->team_away, 'total', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->goalsForTeams($fixture->team_home, $fixture->team_away, 'total', $fixture->date, $leagueId, $homeAway);

        $json['table'] = GoalsTotalsFullTimeAndFirstSecondHalfTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
                'home_away' => $homeAway,
            ]
        );

        return $json;
    }

    public function inFavorGoals(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureModel
            ->maxGoalsForTeams($fixture->date, $leagueId);

        $json['avg'] = $this->fixtureModel
            ->avgGoalsForTeams($fixture->team_home, $fixture->team_away, 'in_favor', $fixture->date, $fixture->league_id, $homeAway);

        $query = $this->fixtureEventModel
            ->goalsForTeams($fixture->team_home, $fixture->team_away, 'in_favor', $fixture->date, $leagueId, $homeAway);

        $json['table'] = GoalsTotalsFullTimeAndFirstSecondHalfTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 3)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
                'home_away' => $homeAway,
            ]
        );

        return $json;
    }

    public function againstGoals(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureModel
            ->maxGoalsForTeams($fixture->date, $leagueId);

        $json['avg'] = $this->fixtureModel
            ->avgGoalsForTeams($fixture->team_home, $fixture->team_away, 'against', $fixture->date, $fixture->league_id, $homeAway);

        $query = $this->fixtureEventModel
            ->goalsForTeams($fixture->team_home, $fixture->team_away, 'against', $fixture->date, $leagueId, $homeAway);

        $json['table'] = GoalsTotalsFullTimeAndFirstSecondHalfTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 3)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
                'home_away' => $homeAway,
            ]
        );

        return $json;
    }

    public function bttsGoals(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $query = $this->fixtureEventModel
            ->bothTeamsToScoreForTeams($fixture->team_home, $fixture->team_away, $fixture->date, $leagueId, $homeAway);

        $json = BTTSFullTimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('BTTS'),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function scorers(Fixture $fixture, int $leagueId, int $homeAway): SupportCollection
    {
        return $this->fixtureEventModel
            ->topScorers($fixture->team_home, $fixture->team_away, $fixture->date, $leagueId, $homeAway)
            ->map(function ($p) {
                $p->playerMedia = $this->mediaSiteModel::find($p->media_site_id);
                return $p;
            });
    }

    public function whoWillScoreFirst(Fixture $fixture, int $leagueId, int $homeAway): SupportCollection
    {
        return $this->fixtureEventModel
            ->whoWillScoreFirst($fixture->team_home, $fixture->team_away, $fixture->date, $leagueId, $homeAway);
    }

    public function goalsPerMinute(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $query = $this->fixtureEventModel
            ->goalsTimesForTeams($fixture->team_home, $fixture->team_away, $fixture->date, $leagueId, $homeAway);

        return GoalsPerMinuteScoredAndConcededPerTeams::matrix(
            Table::layout('90MINUTES'),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );
    }

    public function getStreaks(Fixture $fixture, Request $request): array
    {
        $threshold = $request->get('threshold');

        return $this->parseStreaks($fixture->getStatistics(StatisticGoalType::values(), $threshold)->get(), $request);
    }
}
