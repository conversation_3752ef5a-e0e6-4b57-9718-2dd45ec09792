<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\SimpleServiceInterface;
use App\Http\Clients\JogosHoje\PredictionsHttpClient;
use App\Models\JogosHoje\{Fixture, League};
class FixtureAnalysisService implements SimpleServiceInterface
{
    public const string API_ENDPOINT = '/api/v1/fixtures';
    public const string COMPETITIONS_API_ENDPOINT = '/api/v1/competitions';
    private const int FIXTURES_FETCH_COUNT = 100;

    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly FixturePredictionService $fixturePredictionService,
        private readonly FixtureMarketStatisticsService $fixtureMarketStatisticsService,
        private readonly PredictionsHttpClient $httpClient,
        private readonly League $leagueModel
    ) {
    }

    public function handle(): void
    {
        $currentCompetitions = $this->getActiveCompetitions();
        
        $competitionsApiIds = array_map(function($competition) {
            return $competition->api_football_id;
        }, $currentCompetitions->results);

        $leagueIds = $this->leagueModel->whereIn('api_id', $competitionsApiIds)->pluck('id')->toArray();

        $this->fixtureModel
            ->getFixturesForPredictions($leagueIds)
            ->chunk(self::FIXTURES_FETCH_COUNT, function($fixturesForPredictions) {
                $fixtures = $this->fetchFixturePredictionsFromApi($fixturesForPredictions->pluck('api_id')->toArray());

                foreach($fixtures->results as $result) {
                    $this->fixturePredictionService->handle($result);
                    $this->fixtureMarketStatisticsService->handle($result);
                }
            });
    }

    public function fetchFixturePredictionsFromApi(array $fixturesIds): object
    {
        $queryString = $this->getFixturePredictionsInQueryString($fixturesIds);
        return $this->httpClient->get(self::API_ENDPOINT, $queryString);
    }

    private function getFixturePredictionsInQueryString(array $fixtureIds): string
    {
        return 'api_football_id__in=' . implode(',', $fixtureIds) . '&include_fixtures_in_window=true';
    }

    private function getActiveCompetitions(): object
    {
        $competitions = [];
        $offset = 0;

        do {
            $response = $this->httpClient->get(self::COMPETITIONS_API_ENDPOINT, ['is_active' => true, 'limit' => 100, 'offset' => $offset * 100]);
            $competitions = [...$competitions, ...$response->results];
            $offset++;
        } while ($response->next);

        return (object) ['results' => $competitions];
    }
}
