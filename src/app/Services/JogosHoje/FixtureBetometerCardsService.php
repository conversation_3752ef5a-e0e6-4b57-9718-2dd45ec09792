<?php

namespace App\Services\JogosHoje;

use App\Enums\JogosHoje\StatisticCardType;
use App\Helpers\Matrix\Table;
use App\Helpers\Matrix\Templates\{CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams, RefereeCards};
use App\Http\Resources\JogosHoje\RefereeResource;
use App\Models\JogosHoje\{Fixture, FixtureEvent};
use App\Models\MediaSite;
use App\Traits\{FixturesTrait, JHCacheTrait};
use Illuminate\Http\Request;
use Illuminate\Support\Collection as SupportCollection;

class FixtureBetometerCardsService
{
    use FixturesTrait;
    use JHCacheTrait;

    public function __construct(
        private readonly FixtureEvent $fixtureEventModel,
        private readonly MediaSite $mediaSiteModel,
    ) {
    }

    public function totalCards(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('card', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'total', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'total', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function inFavorCards(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('card', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'against', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'against', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function againstCards(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('card', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'in_favor', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'card', 'in_favor', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function topCards(Fixture $fixture, int $leagueId, int $homeAway): SupportCollection
    {
        return $this->fixtureEventModel
            ->topCards($fixture->team_home, $fixture->team_away, $fixture->date, $leagueId, $homeAway)
            ->map(function ($p) {
                $p->playerMedia = $this->mediaSiteModel->find($p->media_site_id);
                return $p;
            });
    }

    public function referee(Fixture $fixture, int $leagueId, FixtureGameService $gameService): array
    {
        $json = [];

        if (is_null($fixture->referee_id)) {
            return $json;
        }

        $json['avg'] = $this->fixtureEventModel
            ->avgRefereeCardsStatistics($fixture->referee_id, $leagueId, $fixture->date);

        $query = $this->fixtureEventModel
            ->refereeCardsStatistics($fixture->referee_id, $leagueId, $fixture->date);

        $json['table'] = RefereeCards::matrix(
            Table::layout('OVER', range(0, 5)),
            $query
        );

        $json['referee'] = $fixture->referee ? [
            'details' => new RefereeResource($fixture->referee),
            'cards' => $gameService->refereeCards($fixture, $leagueId, $fixture->date),
        ] : null;

        return $json;
    }

    public function getStreaks(Fixture $fixture, Request $request): array
    {
        $threshold = $request->get('threshold');
        return $this->parseStreaks($fixture->getStatistics(StatisticCardType::values(), $threshold)->get(), $request);
    }
}
