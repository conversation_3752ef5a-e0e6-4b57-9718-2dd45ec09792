<?php

namespace App\Services\JogosHoje;

use App\Enums\JogosHoje\Statistic1x2Type;
use App\Helpers\Matrix\Table;
use App\Helpers\Matrix\Templates\PercentageOfWinsDrawsAndLossPerTeamsHalftime;
use App\Models\JogosHoje\{Fixture, FixtureEvent, FixturePrediction};
use App\Traits\{FixturesTrait, JHCacheTrait};
use Illuminate\Http\Request;
use Illuminate\Support\{Arr, Collection};
class FixtureBetometer1x2Service
{
    use FixturesTrait;
    use JHCacheTrait;

    public const string HOME_TEAM_WIN = 'H';
    public const string AWAY_TEAM_WIN = 'A';
    private const int H2H_DEFAULT_ROWS_LIMIT = 5;

    public function __construct(
        private readonly FixtureEvent $fixtureEventModel,
        private readonly BetometerStreaksService $streaksService,
        private readonly Fixture $fixtureModel,
        private readonly FixturePrediction $fixturePredictionModel,
    ) {
    }

    public function getStreaks(Fixture $fixture, Request $request): array
    {
        $threshold = $request->get('threshold');

        $statistics = $fixture->getStatistics(Statistic1x2Type::values(), $threshold)->get();
        return $this->parseStreaks($statistics, $request, $fixture);
    }

    public function matrixOf1x2HTForFixtureTeams(Fixture $fixture, int $leagueId): array
    {
        $queryTeamHome = $this->fixtureEventModel
            ->fulltimeFirstAndSecondHalfResultsForATeam($fixture->team_home, $fixture->date, $leagueId, 1)
            ->get();

        $queryTeamAway = $this->fixtureEventModel
            ->fulltimeFirstAndSecondHalfResultsForATeam($fixture->team_away, $fixture->date, $leagueId, 0)
            ->get();

        return PercentageOfWinsDrawsAndLossPerTeamsHalftime::matrix(
            Table::layout('PERCENTAGE_WINS_DRAWS_LOSS_PER_TEAMS_AND_HALF'),
            $queryTeamHome->concat($queryTeamAway),
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );
    }

    public function streaks(int $homeTeamId, int $awayTeamId): array
    {
        $this->streaksService->handle(['homeTeamId' => $homeTeamId, 'awayTeamId' => $awayTeamId]);

        return $this->streaksService->result;
    }

    public function getH2H(Request $request, Fixture $fixture, ?int $teamId, ?int $leagueId = null): array
    {
        $homeTeamId = $fixture->team_home;
        $awayTeamId = $fixture->team_away;

        $teamsH2H = $this->fixtureModel->getH2H($homeTeamId, $awayTeamId, $fixture->date, $teamId, $leagueId);

        return [
            'counters' => $this->getH2HCounters($homeTeamId, $teamsH2H->get(), $teamId),
            'matches' => $this->parseFixtures(
                collect($teamsH2H->paginate(self::H2H_DEFAULT_ROWS_LIMIT)->items()),
                true,
                $request
            ),
        ];
    }

    private function getH2HCounters(int $homeTeamId, Collection $teamsH2H, ?int $teamId): array
    {
        $homeTeamWinCount = 0;
        $awayTeamWinCount = 0;
        $drawCount = 0;

        foreach ($teamsH2H as $item) {
            if (is_null($item->winner)) {
                $drawCount++;
            } else {
                if (is_null($teamId)) {
                    if ((($item->team_home == $homeTeamId) && ($item->winner == self::HOME_TEAM_WIN)) || (($item->team_away == $homeTeamId) && ($item->winner == self::AWAY_TEAM_WIN))) {
                        $homeTeamWinCount++;
                    } else {
                        $awayTeamWinCount++;
                    }
                } else {
                    if ((($item->team_home == $teamId) && ($item->winner == self::HOME_TEAM_WIN)) || (($item->team_away == $teamId) && ($item->winner == self::AWAY_TEAM_WIN))) {
                        $homeTeamWinCount++;
                    } else {
                        $awayTeamWinCount++;
                    }
                }
            }
        }

        return [
            'home_team_won' => $homeTeamWinCount,
            'away_team_won' => $awayTeamWinCount,
            'draws' => $drawCount,
        ];
    }

    public function predictions(Fixture $fixture): array
    {
        return $this->fixturePredictionModel->whereFixtureId($fixture->id)->get()->map(function ($prediction) {
            $prediction->{'1x2'} = $this->removeUnwantedFields($prediction->{'1x2'});
            $prediction->corners = $this->removeUnwantedFields($prediction->corners);
            $prediction->goals = $this->removeUnwantedFields($prediction->goals);
            $prediction->cards = $this->removeUnwantedFields($prediction->cards);
            return Arr::except($prediction, ['created_at', 'updated_at']);
        })->toArray();
    }

    private function removeUnwantedFields(?array $predictionArray): ?array
    {
        if (is_null($predictionArray)) {
            return null;
        }

        return Arr::except($predictionArray, ['created_at', 'updated_at']);
    }
}
