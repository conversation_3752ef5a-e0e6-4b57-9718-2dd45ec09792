<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\ServiceInterface;
use App\Models\JogosHoje\{Fixture as FixtureModel, FixtureDate, FixtureDateId, League, TimezoneOffset};
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use stdClass;

class FixturesDatesService implements ServiceInterface
{
    public function __construct(
        private readonly FixtureModel $model,
        private readonly FixtureDate $fixtureDateModel,
        private readonly FixtureDateId $fixtureDateIdModel,
        private readonly TimezoneOffset $timezoneOffsetModel,
        private readonly League $leagueModel,
    ) {
    }

    public function handle(?string $type = null): void
    {
        $this->processType($type);
    }

    private function processType(string $type): array
    {
        return match ($type) {
            'dates' => $this->dates(),
            'fixtures' => $this->fixtures(),
            'today' => $this->today(),
            'testing' => $this->testing(),
            default => [],
        };
    }

    private function dates(): array
    {
        dump('Begin: ' . date('Y-m-d H:i:s'));

        $leaguesIds = $this->getLeaguesInUse();

        $fixtures = $this->model
            ->whereIn("fixtures.league_id", $leaguesIds)
            //->whereRaw("DATE(CONVERT_TZ(fixtures.date, '+00:00', '+01:00')) = '2025-01-05'")
            ->groupBy(DB::raw('DATE(fixtures.date)'))
            ->orderBy('fixtures.date', 'desc')
            ->select([
                DB::raw('DATE(fixtures.date) as date'),
            ])
            ->get();

        foreach ($fixtures as $fixture) {
            $this->processDate(Carbon::parse($fixture->date), $leaguesIds);
        }

        dump('End: ' . date('Y-m-d H:i:s'));

        return [];
    }

    private function fixtures(): array
    {
        $start = microtime(true);

        dump('Begin: ' . date('Y-m-d H:i:s'));

        $leaguesIds = $this->getLeaguesInUse();

        $offsets = $this->timezoneOffsetModel
            ->get();
        
        $leaguesIdsStr = join(',', $leaguesIds);
        $fixtures = DB::select("
            SELECT 
                fixtures.id,
                fixtures.date
            FROM fixtures
            WHERE 
                fixtures.league_id IN ({$leaguesIdsStr}) AND 
                fixtures.deleted_at IS NULL
            ORDER BY fixtures.date DESC
        ");        

        foreach ($fixtures as $fixture) {
            dump($fixture->id . ' - ' . $fixture->date);
            $this->processFixture($fixture, $offsets, $leaguesIds);            
        }

        dump('End: ' . date('Y-m-d H:i:s') . ' - ' . (microtime(true) - $start) . 's');

        return [];
    }

    private function testing(): array
    {
        dump('Begin: ' . date('Y-m-d H:i:s'));

        $fixture = $this->model->findOrFail(350886);
        $fixture->update(['date' => '2025-01-05 12:00:00']);

        dump('End: ' . date('Y-m-d H:i:s'));

        return [];
    }

    private function today(): array
    {
        $leaguesIds = $this->getLeaguesInUse();
        $now = Carbon::now();

        dump('Begin: ' . date('Y-m-d H:i:s'));
        $this->processDate($now, $leaguesIds);
        dump('End: ' . date('Y-m-d H:i:s'));

        return [];
    }

    private function processDate(Carbon $originalDate, array $leaguesIds): void
    {
        $offsets = $this->timezoneOffsetModel
            ->get();

        foreach ($offsets as $offset) {
            $date = $originalDate
                ->copy()
                ->setTimezone($offset->offset)
                ->format('Y-m-d');

            $this->process($date, $leaguesIds, $offset);
        }
    }

    public function processFixture(FixtureModel|stdClass $fixture, ?Collection $offsets = null, ?array $leaguesIds = null): void
    {
        $offsets ??= $this->timezoneOffsetModel->get();        
        $leaguesIds ??= $this->getLeaguesInUse();

        $this->fixtureDateIdModel->where('fixture_id', $fixture->id)->delete();

        $originalDate = Carbon::parse($fixture->date);

        foreach ($offsets as $offset) {
            $date = $originalDate
                ->copy()
                ->setTimezone($offset->offset)
                ->format('Y-m-d');

            $this->process($date, $leaguesIds, $offset, $fixture->id);
        }
    }

    public function process(string $date, array $leaguesIds, TimezoneOffset $offset, ?int $fixtureId = null): array
    {
        $fixtureCondition = $fixtureId ? "fixtures.id = {$fixtureId} AND " : '';
        $leaguesIdsStr = join(',', $leaguesIds);

        $query = "
            SELECT 
                fixtures.id
            FROM fixtures
            WHERE 
                {$fixtureCondition}
                fixtures.league_id IN ({$leaguesIdsStr}) AND 
                fixtures.deleted_at IS NULL AND
                DATE(CONVERT_TZ(fixtures.date, '+00:00', ?)) = ?
        ";
        $fixtures = collect(DB::select($query, [$offset->offset, $date]));

        if ($fixtures->count() === 0) {
            return [];
        }

        $fixtureDate = $this->fixtureDateModel->firstOrCreate([
            'date' => $date,
            'offset_id' => $offset->id,
        ]);

        $this->fixtureDateIdModel->insertOrIgnore(
            $fixtures->map(fn($fixture) => [
                'fixture_date_id' => $fixtureDate->id,
                'fixture_id' => $fixture->id,
            ])->toArray()
        );

        $cacheKey = env('APP_ENV') . '-jh-fixtures-dates-' . $date . '-' . $offset->id;

        return Cache::rememberForever($cacheKey, fn() =>
            $this->fixtureDateModel
                ->with(['fixtures'])
                ->where('date', $date)
                ->where('offset_id', $offset->id)
                ->get()
                ->flatMap(fn($fixtureDate) => $fixtureDate->fixtures->pluck('id'))
                ->toArray()
        );
    }

    private function getLeaguesInUse(): array
    {
        return $this->leagueModel
            ->where('in_use', 1)
            ->pluck('id')
            ->toArray();
    }

    public function getFixturesIds(string $date, TimezoneOffset $offset): array
    {
        $cacheKey = env('APP_ENV') . '-jh-fixtures-dates-' . $date . '-' . $offset->id;

        return Cache::rememberForever($cacheKey, function () use ($date, $offset) {
            $fixturesDatesQuery = $this->fixtureDateModel
                ->with(['fixtures'])
                ->where('date', $date)
                ->where('offset_id', $offset->id);

            if ($fixturesDatesQuery->count() === 0) {
                $leaguesIds = $this->getLeaguesInUse();

                return $this->process($date, $leaguesIds, $offset);
            }

            return (clone $fixturesDatesQuery)
                ->get()
                ->flatMap(fn($fixtureDate) => $fixtureDate->fixtures->pluck('id'))
                ->toArray();
        });
    }
}
