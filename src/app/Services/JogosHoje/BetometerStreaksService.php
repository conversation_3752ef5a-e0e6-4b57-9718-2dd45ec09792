<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\DataHandlerServiceInterface;
use App\Models\JogosHoje\Fixture;

class BetometerStreaksService implements DataHandlerServiceInterface
{
    public array $result = [];

    public function __construct(private readonly Fixture $fixtureModel)
    {
    }

    public function handle(array $data): void
    {
        $this->result = [
            'home_team' => $this->fixtureModel->getTeamStreaks($data['homeTeamId'])->get()->all(),
            'away_team' => $this->fixtureModel->getTeamStreaks($data['awayTeamId'])->get()->all()
        ];
    }
}
