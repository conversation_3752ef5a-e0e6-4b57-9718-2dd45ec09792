<?php

namespace App\Services\JogosHoje;

use App\Enums\JogosHoje\CoverageType;
use App\Traits\FixturesTrait;
use \App\Models\JogosHoje\{Fixture, League, Season, Standing};
use App\Services\JogosHoje\{FixtureService, LeagueRoundsService, LeagueService};
use App\Contracts\JogosHoje\Service\ServiceInterface;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Http\Resources\JogosHoje\StandingResource;
use Illuminate\Http\{JsonResponse, Request};
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\{Cache, Date, Log};

class StandingService implements ServiceInterface
{
    use FixturesTrait;
    
    private const string API_ENDPOINT = '/standings';
    private static $translations;

    public static $mapColors = [
        'blue' => 'blue',
        'blue-1' => 'soft_blue',
        'yellow' => 'yellow',
        'yellow-1' => 'soft_yellow',
        'green' => 'green',
        'green-1' => 'soft_green',
        'orange' => 'orange',
        'orange-1' => 'soft_orange',
        'red' => 'red',
        'red-1' => 'soft_red',
    ];

    public function __construct(
        private readonly Fixture $fixtureModel,
        private readonly Standing $standingModel,
        private readonly League $leagueModel,
        private readonly LeagueService $leagueService,
        private readonly SeasonService $seasonService,
        private readonly TeamService $teamService,
        private readonly CoverageService $coverageService,
        private readonly FootballHttpClient $footballHttpClient,
        private readonly Season $seasonModel,
        private readonly LeagueService $service,
        private readonly LeagueRoundsService $leagueRoundsService,
        private readonly FixtureService $fixtureService,
    ) {
    }

    public function handle(string $type): void
    {
        self::$translations = self::getTranslationData();
        $standings = $this->fetchStandingsByType($type);
        $this->process($standings);
    }

    public function fetchStandingsByType(string $type): array
    {
        $apiData = [];
        if ($type === 'live') {
            /**
             * TODO: should we change the next query to include only fixtures updated in the last 10 minutes?
             */
            $fixturesLive = $this->fixtureModel->getSeasonAndLeagues([
                'current' => 1,
                'fixtures.is_live' => 1
            ]);

            /** @var Fixture[] $fixturesLive */
            foreach ($fixturesLive as $fixture) {
                /*
                if($this->shouldSkipStandingsFetch($fixture)) {
                    continue;
                }
                */
                $response = $this->fetchApiStandings([
                    'league' => $fixture->league,
                    'season' => $fixture->season,
                ]);

                $apiData[] = $response;
            }
        } elseif ($type === 'daily') {
            $leagues = $this->leagueService->fetchLeagues(['current' => 'true']);

            $yesterdayFixtures = $this->fixtureModel                
                ->whereRaw('DATE(fixtures.date) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))')
                ->select(['fixtures.league_id'])
                ->distinct()
                ->get()
                ->map(fn($item) => $item->league_id)
                ->toArray();

            $inUseLeaguesIds = $this->leagueModel
                ->where('in_use', 1)
                //->join('coverages', 'coverages.league_id', '=', 'leagues.id')
                //->where('coverages.has_standings', true)
                ->whereIn('id', $yesterdayFixtures)
                ->select(['api_id'])
                ->get()
                ->map(fn($item) => $item->api_id)
                ->toArray();

            foreach ($leagues as $league) {
                if (empty($league->seasons) || !in_array($league->league->id, $inUseLeaguesIds)) {
                    continue;
                }

                $response = $this->fetchApiStandings([
                    'league' => $league->league->id,
                    'season' => $league->seasons[0]->year,
                ]);

                if (!empty($response)) {
                    $apiData = array_merge($apiData, $response);
                }

                usleep(100);
            }
        } elseif ($type === 'all-seasons') {
            $leagues = $this->leagueModel
                ->where('in_use', 1)
                ->orderBy('leagues.id')
                //->join('coverages', 'coverages.league_id', '=', 'leagues.id')
                //->where('coverages.has_standings', true)
                ->get();

            $seasons = $this
                ->seasonModel
                ->orderBy('season', 'desc')
                ->where('season', '<=', 2013)
                ->get();

            foreach ($seasons as $season) {
                foreach ($leagues as $league) {
                    $this->standingModel
                        ->where('league_id', $league->id)
                        ->where('season_id', $season->id)
                        ->forceDelete();

                    $response = $this->fetchApiStandings([
                        'league' => $league->api_id,
                        'season' => $season->season,
                    ]);

                    $parsedStandingsData = $this->prepareData($response);
                    $this->standingModel->persistStandings($parsedStandingsData);

                    usleep(200);
                }
            }
        }

        return $apiData;
    }

    private function shouldSkipStandingsFetch(Fixture $fixture): bool
    {
        if(!$this->coverageService->hasCoverage($fixture->league_id, CoverageType::HAS_STANDINGS->value)) {
            Log::info('Fetching standings for the uncovered league has been skipped. Fixture ID: ' . $fixture->id);
            return true;
        }
        return false;
    }

    public function fetchApiStandings(array $query): array
    {
        return $this->footballHttpClient->get(self::API_ENDPOINT, $query);
    }

    public function process(array $standings): void
    {
        $parsedStandingsData = $this->prepareData($standings);
        $this->standingModel->persistStandings($parsedStandingsData);
    }

    public function prepareData(array $standings): array
    {
        $data = [];

        foreach ($standings as $item) {
            $league = $item->league;
            $season = $item->league->season;
            $standings = $item->league->standings;

            $leagueId = $this->leagueService->findByApiId($league->id)->pluck('id')->first();
            $seasonId = $this->seasonService->findByYear($season)->pluck('id')->first();

            foreach ($standings as $table) {
                foreach ($table as $row) {
                    if (is_null($row->team->id)) {
                        continue;
                    }

                    $teamId = $this->teamService->findByApiId($row->team->id)->pluck('id')->first();
                    if ($teamId) {
                        $data[] = [
                            'season_id' => $seasonId,
                            'league_id' => $leagueId,
                            'team_id' => $teamId,
                            'group' => ($row->group ?? null),
                            'description' => $row->description,
                            'rank' => $row->rank ?? 0,
                            'points' => $row->points ?? 0,
                            'goals_diff' => $row->goalsDiff ?? 0,
                            'form' => $row->form,
                            'status' => $row->status,
                            'played' => $row->all->played ?? 0,
                            'wins' => $row->all->win ?? 0,
                            'draws' => $row->all->draw ?? 0,
                            'loses' => $row->all->lose ?? 0,
                            'goals_for' => $row->all->goals->for ?? 0,
                            'goals_against' => $row->all->goals->against ?? 0,
                            'last_updated' => date('Y-m-d H:i:s', strtotime($row->update)),
                            'extra' => $row->description,
                            'color' => self::getColor($leagueId, $row->description),
                        ];
                    }
                }
            }
        }

        return $data;
    }

    public static function getPromotions($participants, $checkGroup = false)
    {
        $promotions = [];
        $promotionCount = 0;
        $promotionDesc = null;

        foreach ($participants as $participant) {
            $rank = $participant['rank'];
            $desc = $participant['description'];

            $mainCondition = empty($desc) || str_contains($desc, 'Ranking of third-placed teams');

            if (!empty($checkGroup)) {
                $mainCondition = empty($desc)
                    || str_contains($desc, 'Ranking of third-placed teams')
                    || $checkGroup !== $participant['group'];
            }

            if ($mainCondition) {
                continue;
            }

            if (str_contains($desc, 'Rebaixamento') && str_contains($promotionDesc, 'Rebaixamento')) {
                $promotions[$promotionCount - 1]['range'][1] = $rank;
                $promotionDesc = $desc;
                continue;
            }

            if ($desc === $promotionDesc) {
                $promotions[$promotionCount - 1]['range'][1] = $rank;
                continue;
            }

            $promotionCount++;
            $promotionDesc = $desc;
            $promotions[$promotionCount - 1]['range'][0] = $rank;
            $promotions[$promotionCount - 1]['description'] = $desc;
        }

        return $promotions;
    }

    public static function processStandingsData($standings, $type = 'short', $group = false): array
    {
        $standings = $standings->sortBy('rank');
        $participants = [];

        foreach ($standings as $standing) {
            $team = $standing->team;

            $teamData = [
                'id' => $team->id,
                'api_id' => $team->api_id,
                'name' => $team->name,
                'slug' => $team->slug,
                'country_slug' => $team->country->slug,
            ];

            $participant = [
                'rank' => $standing->rank,
                'played' => $standing->played,
                'goals_diff' => $standing->goals_diff,
                'points' => $standing->points,
                'description' => $standing->description,
                'team' => $teamData,
            ];

            if ($type === 'full') {
                $participant['form'] = $standing->form;
                $participant['wins'] = $standing->wins;
                $participant['draws'] = $standing->draws;
                $participant['loses'] = $standing->loses;
                $participant['goals_for'] = $standing->goals_for;
                $participant['goals_against'] = $standing->goals_against;
            }

            if ($group) {
                $participant['group'] = $standing->group;
            }

            $participants[] = $participant;
        }

        return [
            'promotions' => self::getPromotions($participants),
            'participants' => $participants,
        ];
    }

    public static function getTranslationData()
    {
        return Cache::remember(
            'standings-translation',
            Date::now()->addMonth(),
            fn() => SearchReplaceService::getFileData('/Tradução-qualificação-unique.json')
        );
    }

    public function getFixtureStandings($params): array
    {
        $league = $this->leagueModel->find($params['leagueId']);

        $leagueStandings = self::processStandingsData($league->standings, 'full');

        $fixtureStandings = [
            'promotions' => $leagueStandings['promotions'],
            'participants' => [],
        ];

        foreach ($leagueStandings['participants'] as $participant) {
            if (in_array($participant['team']['id'], [$params['homeTeamId'], $params['awayTeamId']])) {
                $fixtureStandings['participants'][] = $participant;
            }
        }

        return !empty($fixtureStandings['participants']) ? $fixtureStandings : [];
    }

    public function getQualificationData(array $leagueRounds, League $league, Request $request, ?Fixture $fixtureLastRound = null): array
    {
        $qualificationData = [];

        if (sizeof($leagueRounds['qualification'])) {
            $qualificationGames = $this->fixtureService->getEndedFixturesForSpecificRounds($league->id, $leagueRounds['qualification'], $request);
            $qualificationData = $this->parseQualificationAndKnockoutGames($qualificationGames);
        }

        return [
            'titles' => $leagueRounds['qualification'],
            'current' => $this->checkIfIsCurrentPhase($fixtureLastRound, $leagueRounds['qualification']),
            'data' => $qualificationData,
        ];
    }

    public function getStandingsData(array $leagueRounds, League $league, Request $request, ?Fixture $fixtureLastRound = null): array
    {
        $currentPhase = null;
        $query = $this->service->standings($league->id, $request)
            ->map(function ($item) {
                $item->trans = $this->getTranslation($item->extra, true);
                return $item;
            });

        $standingTitles = $this->leagueRoundsService->extractTitlesFromRounds($leagueRounds['standings']);

        if (($currentPhase = $this->checkIfIsCurrentPhase($fixtureLastRound, $leagueRounds['standings'], true)) && (!$request->get('round')) && sizeof($standingTitles) > 1) {
            $request->merge(['round' => $currentPhase]);
        }

        return [
            'titles' => $standingTitles,
            'current' => $currentPhase,
            'data' => $this->handleGroups($query),
        ];
    }

    public function getBracketsData(array $leagueRounds, League $league, Request $request, ?Fixture $fixtureLastRound = null): array
    {
        $bracketsData = [];

        if (sizeof($leagueRounds['brackets'])) {
            $bracketsGames = $this->fixtureService->getEndedFixturesForSpecificRounds($league->id, $leagueRounds['brackets'], $request);
            $bracketsData = $this->parseQualificationAndKnockoutGames($bracketsGames, true);
        }

        return [
            'titles' => $leagueRounds['brackets'],
            'current' => $this->checkIfIsCurrentPhase($fixtureLastRound, $leagueRounds['brackets']),
            'data' => $bracketsData,
        ];
    }

    private function getColor(int $leagueId, ?string $originalDescription): ?string
    {
        if (!$originalDescription) {
            return null;
        }

        $return = null;

        $handle = fopen(database_path('/standings/colors.txt'), 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                [$countryLeague, $description, $color] = explode('|', $line);

                $color = trim($color);

                [$country, $league] = explode('=>', $countryLeague);

                $league = trim($league);

                $fileLeagueId = (int) trim(str_replace('#', '', explode(' ', $league)[0]));

                if ((StandingService::getTranslation($originalDescription, true) || StandingService::getTranslation($originalDescription)) && ($description == $originalDescription) && ($leagueId == $fileLeagueId)) {
                    $return = $color;
                    break;
                }
            }

            fclose($handle);
        }

        if (!$return) {
            $handle = fopen(database_path('/standings/missing.txt'), "r");
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    [$seasonIdLeagueId, $position, $extra, $color] = explode('|', $line);

                    [$seasonId, $fileLeagueId] = explode('-', $seasonIdLeagueId);

                    $color = strtolower(trim($color));

                    if (($leagueId == $fileLeagueId) && ($originalDescription == $extra)) {
                        $return = self::$mapColors[$color];
                        break;
                    }
                }
            }
        }

        return $return;
    }

    public static function getTranslation(?string $description, bool $searchForOriginalTranslation = false): mixed
    {
        $translations = self::getTranslationData();

        $translatedField = $searchForOriginalTranslation ? 'description' : 'NewDescription';
        $returnTranslatedField = $searchForOriginalTranslation ? 'NewDescription' : 'description';

        $str = null;
        foreach ($translations as $translation) {
            if ($translation[$translatedField] === $description) {
                $str = $translation[$returnTranslatedField];
                break;
            }
        }

        return $str;
    }

    public function handleGroups(Collection $col): array|AnonymousResourceCollection
    {
        $hasGroups = $this->hasGroups($col);
        
        if (!$hasGroups) {
            return StandingResource::collection($col);
        }

        return $col
            ->groupBy('group')
            ->map(fn($items) => StandingResource::collection($items))
            ->sortKeys()
            ->toArray();
    }

    public function hasGroups(Collection $col): bool
    {
        $hasGroups = false;

        foreach ($col as $item) {
            if (str_contains($item->group, 'Group ')) {
                $hasGroups = true;
                break;
            }
        }

        return $hasGroups;
    }
}
