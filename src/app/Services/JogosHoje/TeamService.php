<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\ServiceInterface;
use App\Enums\JogosHoje\Statistic1x2Type;
use App\Helpers\Matrix\Table;
use App\Helpers\Matrix\Templates\{BTTSFullTimeAndFirstSecondHalfHomeAway, CardsFullTtimeAndFirstSecondHalfHomeAway, CornersHomeAway, GoalsPerMinuteScoredAndConcededHomeAway, GoalsTotalsFullTimeAndFirstSecondHalfHomeAway, PercentageOfWinsDrawsAndLossHomeAway};
use App\Helpers\Media;
use App\Http\Clients\JogosHoje\FootballHttpClient;
use App\Http\Middleware\ApiHelpers;
use App\Models\JogosHoje\Views\VwTeamAverageCards;
use App\Models\JogosHoje\{Country, Fixture, FixtureEvent, FixtureStatistic, League, LeagueSeason, LeagueTeam, Standing, Statistic, Team, TeamPlayer, Venue};
use App\Models\MediaSite;
use App\Traits\FixturesTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class TeamService implements ServiceInterface
{
    use FixturesTrait;

    private const string API_ENDPOINT = '/teams';
    private const string API_STATS_ENDPOINT = '/teams/statistics';

    public function __construct(
        private readonly Team $model,
        private readonly LeagueSeason $leagueSeasonModel,
        private readonly LeagueTeam $leagueTeamModel,
        private readonly Country $countryModel,
        private readonly CountryService $countryService,
        private readonly LeagueTeamService $leagueTeamService,
        private readonly FootballHttpClient $httpClient,
        private readonly VenuesService $venuesService,
        private readonly Venue $venueModel,

        private readonly Fixture $fixtureModel,
        private readonly FixtureEvent $fixtureEventModel,
        private readonly FixtureStatistic $fixtureStatisticModel,
        private readonly Standing $standingModel,
        private readonly TeamPlayer $teamPlayerModel,
        private readonly VwTeamAverageCards $vwTeamAverageCardsModel,
        private readonly Statistic $statisticModel,
        private readonly MediaSite $mediaSite,
    ) {
    }

    public function handle(string $type = ''): void
    {
        if ($type === 'default') {
            $this->saveTeams($type);
        } elseif (in_array($type, ['statistics-current', 'statistics-all'])) {
            $this->saveTeamsStatistics($type);
        }
    }

    public function saveTeamsStatistics(string $type): void
    {
        $leaguesTeams = [];

        if ($type === 'statistics-all') {
            $leaguesTeams = $this->leagueTeamModel::where(function ($q) {
                $q->whereDate('team_stats_at', '<', Carbon::now()->subDay()->format('Y-m-d H:i:s'))
                    ->orWhereNull('team_stats_at');
            })
                ->whereNotNull('team_id')
                ->with(['team:id,api_id', 'season:id,season', 'league:id,api_id'])
                ->orderBy('league_id', 'ASC')
                ->distinct('team_id')
                ->select(['team_id', 'season_id', 'league_id'])
                //->limit(1) // disable it later. Just for testing purposes to not waste API credits
                ->get();
        } else {
            $leaguesTeams = $this->leagueTeamModel->getTeamsWithCurrentSeason()
                ->join('leagues', 'leagues.id', '=', 'league_teams.league_id')
                ->where(function ($q) {
                    $q->whereDate('team_stats_at', '<', Carbon::now()->subDay()->format('Y-m-d H:i:s'))
                        ->orWhereNull('team_stats_at');
                })
                ->where('leagues.in_use', 1)
                ->orderBy('league_id', 'ASC')
                //->limit(1) // disable it later. Just for testing purposes to not waste API credits
                ->get();
        }

        foreach ($leaguesTeams as $item) {
            $stats = $this->fetchStatistics([
                'team' => $item->team->api_id,
                'league' => $item->league->api_id,
                'season' => $item->season->season,
            ]);

            $this->handleResponse($stats, $item, $type);

            $this->leagueTeamModel
                ->where('league_id', $item->league->id)
                ->where('team_id', $item->team->id)
                ->where('season_id', $item->season->id)
                ->update([
                    'team_stats_at' => now(),
                ]);
        }
    }

    public function saveTeams(string $type): void
    {
        $leagues = $this->leagueSeasonModel::getLeaguesWithCurrentSeason()
            ->join('leagues', 'leagues.id', '=', 'league_seasons.league_id')
            ->where(function ($q) {
                $q->whereDate('team_at', '<', Carbon::now()->subDay()->format('Y-m-d H:i:s'))
                    ->orWhereNull('team_at');
            })
            ->where('leagues.in_use', 1)
            ->orderBy('league_id', 'ASC')
            ->get();

        foreach ($leagues as $item) {
            $teams = $this->fetchTeams([
                'league' => $item->league->api_id,
                'season' => $item->season->season,
            ]);

            $this->handleResponse($teams, $item, $type);
        }
    }

    public function handleResponse(mixed $apiData, LeagueSeason|LeagueTeam $item, string $type): void
    {
        $params = [
            'league_id' => $item->league->id,
            'season_id' => $item->season->id,
            'team_id' => isset($item->team) ? $item->team->id : ApiHelpers::issetCheck($item, ['team', 'id'])
        ];

        $type === 'default' && $this->bulkUpdateApiData($apiData);
        $this->leagueTeamService->bulkUpdateApiData($apiData, $params, $type);
    }

    public function fetchTeams(array $query): array
    {
        return $this->httpClient->get(self::API_ENDPOINT, $query);
    }

    public function fetchStatistics(array $query): mixed
    {
        return $this->httpClient->get(self::API_STATS_ENDPOINT, $query);
    }

    public function bulkUpdateApiData(array $apiData, ?League $league = null): void
    {
        $data = $this->processApiData($apiData, $league);

        foreach ($data as $item) {
            $this->model->persistTeam($item);
        }
    }

    public function processApiData(array $apiData, ?League $league = null): array
    {
        $data = [];

        foreach ($apiData as $item) {
            Log::info($item->team->name);
            $nationalTeam = $item->team->national;
            $countryName = $nationalTeam ? 'Selections' : $item->team->country;

            if ((!$countryName) && $league) {
                $countryName = $league->country->api_name;
            }

            if (!$countryName && !$nationalTeam) {
                continue;
            }

            $country = $this->countryModel->getByApiName($countryName);

            if (!$country) {
                $country = new \stdClass();
                $country->name = $countryName;
                $country->api_name = $countryName;
                $country->slug = Str::slug($countryName);
                $this->countryService->updateOrCreateCountries([
                    $country
                ]);
                $country = $this->countryModel->getByApiName($countryName);
            }

            $venue = (object) [...(array) $item->venue, 'country' => $countryName];
            $this->venuesService->process([$venue]);
            $venueId = $this->venueModel->where('api_id', $item->venue->id)?->pluck('id')->first();

            $data[] = [
                'create' => [
                    'api_name' => $item->team->name,
                    'api_id' => $item->team->id,
                    'name' => $item->team->name,
                    'slug' => Str::slug($item->team->name),
                    'national' => $item->team->national,
                    'country_id' => $country->id,
                    'updated_at' => now()->format('Y-m-d H:i:s'),
                    'venue_id' => $venueId,
                ],
                'update' => [
                    'api_name' => $item->team->name,
                    'api_id' => $item->team->id,
                    'national' => $item->team->national,
                    'country_id' => $country->id,
                    'updated_at' => now()->format('Y-m-d H:i:s'),
                    'venue_id' => $venueId,
                ]
            ];
        }

        return $data;
    }

    public function findByApiId(int $apiId): ?Builder
    {
        if (empty($apiId)) {
            return null;
        }

        return $this->model::where('api_id', $apiId);
    }

    public function performance(int $teamId, Request $request): array
    {
        $section = $request->get('section', 'form');
        $leagueId = $request->get('league_id');
        $homeAway = $request->get('home_away', -1);

        $nextMatch = $this->fixtureModel
            ->nextTeamFixtures($teamId)
            ->first();

        return match ($section) {
            'stats' => $this->fixtureStatisticModel
                ->attackPassesAndDefenseStatisticsForATeam($teamId, $leagueId, $homeAway),
            'table' => PercentageOfWinsDrawsAndLossHomeAway::matrix(
                Table::layout('PERCENTAGE_WINS_DRAWS_LOSS'),
                $this->fixtureEventModel
                    ->fulltimeFirstAndSecondHalfResultsForATeam($teamId, now(), $leagueId)
                    ->get()
            ),
            'streaks' => $this->parseStreaks($this->statisticModel->streaksForATeam($teamId, Statistic1x2Type::values(), $nextMatch), $request),
            default => $this->fixtureModel
                ->teamPerformance($teamId)
                ->get()
                ->sortBy('date')
                ->values()
                ->toArray()
        };
    }

    public function overviewGoals(int $teamId, int $homeAway, ?string $leagueId = null): object
    {
        return $this->fixtureModel
            ->avgGoalsForATeam($teamId, $homeAway, $leagueId);
    }

    public function goals(int $teamId, ?string $leagueId = null): array
    {
        $query = $this->fixtureEventModel
            ->goalsTotalsForATeam($teamId, $leagueId);

        return GoalsTotalsFullTimeAndFirstSecondHalfHomeAway::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query
        );
    }

    public function btts(int $teamId, ?string $leagueId = null): array
    {
        $query = $this->fixtureEventModel
            ->bothTeamsToScoreStatisticsForATeam($teamId, $leagueId);

        return BTTSFullTimeAndFirstSecondHalfHomeAway::matrix(
            Table::layout('BTTS'),
            $query
        );
    }

    public function goalsPerMinuteForATeam(int $teamId, ?string $leagueId = null): array
    {
        $query = $this->fixtureEventModel
            ->goalsTimesForATeam($teamId, $leagueId);

        return GoalsPerMinuteScoredAndConcededHomeAway::matrix(
            Table::layout('90MINUTES'),
            $query
        );
    }

    public function overviewCorners(int $teamId, ?string $leagueId = null, ?int $homeAway = null): array
    {
        return $this->fixtureStatisticModel
            ->avgCornersForATeam($teamId, $leagueId, $homeAway);
    }

    public function corners(int $teamId, ?string $leagueId = null): array
    {
        $query = $this->fixtureEventModel
            ->cornersForATeam($teamId, $leagueId);

        return CornersHomeAway::matrix(
            Table::layout('UNDER_OVER', [4, 6, 8, 10]),
            $query
        );
    }

    public function overviewCards(int $teamId, ?string $leagueId = null, ?int $homeAway = null): array
    {
        return $this->fixtureStatisticModel
            ->avgCardsForATeam($teamId, $leagueId, $homeAway);
    }

    public function cards(int $teamId, ?string $leagueId = null): array
    {
        $query = $this->fixtureEventModel
            ->cardsForATeam($teamId, $leagueId);

        return CardsFullTtimeAndFirstSecondHalfHomeAway::matrix(
            Table::layout('UNDER_OVER', range(1, 4)),
            $query
        );
    }

    public function standings(int $teamId, Request $request): array
    {
        $nextMatch = $this->fixtureModel
            ->nextTeamFixtures($teamId)
            ->first();

        $currentLeagueAndSeason = $this->leagueTeamModel
            ->getCurrentSeasonForMainLeague($teamId);

        $defaultLeagueId = $nextMatch && !$nextMatch->league->is_cup ? $nextMatch->league_id : $currentLeagueAndSeason->league_id;
        $defaultSeasonId = $nextMatch && !$nextMatch->league->is_cup ? $nextMatch->season_id : $currentLeagueAndSeason->season_id;

        $leagueId = $request->get('league_id', $defaultLeagueId);
        $seasonId = $request->get('season_id', $defaultSeasonId);

        return [
            'next_match' => $nextMatch,
            'league_id' => $leagueId,
            'season_id' => $seasonId,
            'standings' => $this->standingModel
                ->standingsForATeam($teamId, $leagueId, $seasonId)
                ->get()
        ];
    }

    public function matches(int $teamId, Request $request): array
    {
        $limit = $request->get('limit', 10);
        $offset = $request->get('offset', 0);

        $query = $this->fixtureModel
            ->nextTeamFixtures($teamId, limit: $limit, request: $request)
            ->get();

        if (($query->count() != $limit) && ($offset == 0)) {
            $queryLastGames = $this->fixtureModel
                ->lastTeamFixtures($teamId, $request, $limit)
                ->get();

            $query = $query->merge($queryLastGames);
        }

        $result = [];

        $result['fixtures'] = $this->parseTeamFixtures(
            $query->sortBy('date')->values(),
            $request
        );

        $result['pagination'] = [
            'has_next' => $request->attributes->get('has_next', false),
            'has_previous' => $request->attributes->get('has_previous', false)
        ];

        return $result;
    }

    public function featuredMatch(int $teamId): ?object
    {
        return $this->fixtureModel
            ->nextTeamFixtures($teamId, true)
            ->first();
    }

    public function players(int $teamId, ?string $param = null): array
    {
        return $this->teamPlayerModel
            ->playerStatisticsPerTeam($teamId, $param)
            ->map(function ($item) {
                $playerMedia = (object) [
                    'filename' => $item->player_media_filename,
                    'alt' => $item->player_media_alt,
                    'width' => $item->player_media_width,
                    'height' => $item->player_media_height,
                    'guid' => $item->player_media_guid,
                ];
                $item->media = Media::getLogo($playerMedia) ?? null;

                return Media::removeUnusedFields($item);
            })
            ->sortByDesc('value')
            ->values()
            ->all();
    }

    public function differentLeagueRounds(Collection $rounds): array
    {
        $titles = [];

        foreach ($rounds as $round) {
            if (strpos($round->group, ':') !== false) {
                $exp = explode(':', $round->group);
                $str = '';

                foreach ($exp as $index => $piece) {
                    if ($index > 0) {
                        $str .= (string) ' ' . trim($piece);
                    }
                }

                $titles[] = substr($str, 1);
            }
        }

        if (empty($titles)) {
            $groupKeys = $rounds->groupBy('group')->keys();
            if (sizeof($groupKeys) > 1) {
                $titles = $groupKeys->values()->toArray();
            }
        }

        return array_values(array_unique($titles));
    }

    public function getOnboardingTeams(array $leagues): Collection
    {
        if (empty($leagues)) {
            return collect([]);
        }

        $teamIds = collect([]);

        foreach ($leagues as $league) {
            if ($teamIds->count() > 20) {
                break;
            }

            $leagueTeams = $this->leagueTeamModel
                ->with(['team'])
                ->join('league_seasons', function ($join) {
                    $join->on('league_seasons.league_id', '=', 'league_teams.league_id')
                        ->on('league_seasons.season_id', '=', 'league_teams.season_id')
                        ->where('league_seasons.current', '=', 1);
                })
                ->where('league_teams.league_id', $league->id)
                ->get();

            foreach ($leagueTeams as $item) {
                if (!$teamIds->contains($item->team->id)) {
                    $teamIds->push($item->team->id);
                }
            }
        }

        return $this->model
            ->whereIn('id', $teamIds->take(20))
            ->get();
    }
}
