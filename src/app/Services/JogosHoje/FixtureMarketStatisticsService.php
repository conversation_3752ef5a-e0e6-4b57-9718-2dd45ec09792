<?php

namespace App\Services\JogosHoje;

use App\Contracts\JogosHoje\Service\DataHandlerServiceInterface;
use App\Enums\JogosHoje\StreakTranslation;
use App\Models\JogosHoje\{Fixture, Market, Statistic, StatisticDetails, StatisticDetailsStreak, Team};
use Illuminate\Support\Facades\{Cache, DB};

readonly class FixtureMarketStatisticsService implements DataHandlerServiceInterface
{
    public function __construct(
        private Fixture $fixtureModel,
        private Market $marketModel,
        private Statistic $statisticModel,
        private StatisticDetails $statisticDetailsModel,
        private Team $teamModel,
        private StatisticDetailsStreak $statisticDetailsStreakModel,
    ) {
    }

    public function handle(array|object $data): void
    {
        $fixtureId = $this->fixtureModel->whereApiId($data->api_football_id)->pluck('id')->first();
        if ($fixtureId) {
            Cache::tags("Cards-{$fixtureId}")->flush();
            Cache::tags("1X2-{$fixtureId}")->flush();
            Cache::tags("Goals-{$fixtureId}")->flush();
            Cache::tags("Corners-{$fixtureId}")->flush();

            DB::transaction(function () use ($data, $fixtureId) {
                foreach ($data->statistics->markets as $statistics) {
                    $market = $this->marketModel->firstOrCreate([
                        'type' => $statistics->market_type,
                        'value' => $statistics->market_value,
                    ]);

                    foreach ($statistics->statistics as $statistic) {
                        $statisticOne = $statistic->statistic_1;
                        $statisticTwo = $statistic->statistic_2;

                        $statisticOneTeamId = $this->teamModel->whereApiId($statisticOne->team->api_football_id)
                            ->pluck('id')->first();
                        $statisticDetailsOneData = $this->getStatisticDetailsData(
                            $statistic->statistic_1,
                            $statisticOneTeamId
                        );
                        $statisticDetailsOneData['happened'] = $statisticOne->happened;

                        $statisticDetailsOneRow = $this->statisticDetailsModel->updateOrCreate(
                            [
                                //'pretty_name' => $statisticDetailsOneData['pretty_name'],
                                'config_type_id' => $statisticDetailsOneData['config_type_id'],
                                'config_window_size' => $statisticDetailsOneData['config_window_size'],
                                'config_threshold' => $statisticDetailsOneData['config_threshold'],
                                'team_id' => $statisticDetailsOneData['team_id'],
                                'fixture_id' => $fixtureId
                            ],
                            $statisticDetailsOneData
                        );

                        if ($statisticDetailsOneRow) {
                            foreach ($statisticOne->fixtures_in_window as $fixtureInWindow) {
                                $fixture = $this->fixtureModel->whereApiId($fixtureInWindow->api_football_id)->first();

                                if ($fixture) {
                                    $statisticDetailsStreak = $this->statisticDetailsStreakModel->firstOrNew([
                                        'statistic_details_id' => $statisticDetailsOneRow->id,
                                        'fixture_id' => $fixture->id,
                                    ]);

                                    $statisticDetailsStreak->happened = $fixtureInWindow->nested_statistic_happened;
                                    $statisticDetailsStreak->save();
                                }
                            }
                        }

                        $statisticDetailsTwoRow = null;
                        if ($statisticTwo) {
                            $statisticTwoTeamId = $this->teamModel->whereApiId($statisticTwo->team->api_football_id)
                                ->pluck('id')->first();
                            $statisticDetailsTwoData = $this->getStatisticDetailsData(
                                $statisticTwo,
                                $statisticTwoTeamId
                            );
                            $statisticDetailsTwoData['happened'] = $statisticTwo->happened;

                            $statisticDetailsTwoRow = $this->statisticDetailsModel->updateOrCreate(
                                [
                                    //'pretty_name' => $statisticDetailsTwoData['pretty_name'],
                                    'config_type_id' => $statisticDetailsTwoData['config_type_id'],
                                    'config_window_size' => $statisticDetailsTwoData['config_window_size'],
                                    'config_threshold' => $statisticDetailsTwoData['config_threshold'],
                                    'team_id' => $statisticDetailsTwoData['team_id'],
                                    'fixture_id' => $fixtureId
                                ],
                                $statisticDetailsTwoData
                            );

                            if ($statisticDetailsTwoRow) {
                                foreach ($statisticTwo->fixtures_in_window as $fixtureInWindow) {
                                    $fixture = $this->fixtureModel->whereApiId(
                                        $fixtureInWindow->api_football_id
                                    )->first();

                                    if ($fixture) {
                                        $statisticDetailsStreak = $this->statisticDetailsStreakModel->firstOrNew([
                                            'statistic_details_id' => $statisticDetailsTwoRow->id,
                                            'fixture_id' => $fixture->id,
                                        ]);
                                        $statisticDetailsStreak->happened = $fixtureInWindow->nested_statistic_happened;
                                        $statisticDetailsStreak->save();
                                    }
                                }
                            }
                        }

                        $this->statisticModel->updateOrCreate(
                            [
                                'market_id' => $market->id,
                                'fixture_id' => $fixtureId,
                                'pretty_name' => $statistic->pretty_name,
                            ],
                            [
                                'score' => $statistic->score,
                                'type' => $statistic->type,
                                'statistic_details_1_id' => $statisticDetailsOneRow->id,
                                'statistic_details_2_id' => $statisticDetailsTwoRow ? $statisticDetailsTwoRow->id : null,
                            ]
                        );
                    }
                }
            });
        }
    }

    private function getStatisticDetailsData(object $statistic, int $teamId): array
    {
        return [
            'value' => $statistic->value,
            'count' => $statistic->count,
            'pretty_name' => $statistic->pretty_name,
            'team_id' => $teamId,
            'config_pretty_name' => $this->getStreakPrettyName($statistic->config->type_id, (float) $statistic->config->threshold),
            'config_type_id' => $statistic->config->type_id,
            'config_window_size' => $statistic->config->window_size,
            'config_threshold' => $statistic->config->threshold,
        ];
    }

    private function getStreakPrettyName(string $key, float $threshold): string
    {
        return sprintf(StreakTranslation::getTranslationByKey($key), number_format($threshold, 1));
    }
}
