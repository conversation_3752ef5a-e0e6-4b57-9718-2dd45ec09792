<?php

namespace App\Services\JogosHoje;

use App\Enums\JogosHoje\StatisticCornerType;
use App\Helpers\Matrix\Table;
use App\Helpers\Matrix\Templates\CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams;
use App\Models\JogosHoje\{Fixture, FixtureEvent};
use App\Traits\{FixturesTrait, JHCacheTrait};
use Illuminate\Http\Request;

class FixtureBetometerCornersService
{
    use JHCacheTrait, FixturesTrait;

    public function __construct(
        private readonly FixtureEvent $fixtureEventModel,
    ) {
    }

    public function totalCorners(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('corner', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'total', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'total', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(4, 10)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function inFavorCorners(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('corner', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'in_favor', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'against', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(4, 10)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function againstCorners(Fixture $fixture, int $leagueId, int $homeAway): array
    {
        $json = [];

        $json['avg_max'] = $this->fixtureEventModel
            ->maxCardsOrCornersForTeams('corner', $fixture->date, $leagueId);

        $json['avg'] = $this->fixtureEventModel
            ->avgCardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'against', $fixture->date, $leagueId, $homeAway);

        $query = $this->fixtureEventModel
            ->cardsOrCornersForTeams($fixture->team_home, $fixture->team_away, 'corner', 'in_favor', $fixture->date, $leagueId, $homeAway);

        $json['table'] = CardsOrCornersFullTtimeAndFirstSecondHalfPerTeams::matrix(
            Table::layout('UNDER_OVER', range(4, 10)),
            $query,
            [
                'teams' => [$fixture->homeTeam, $fixture->awayTeam],
            ]
        );

        return $json;
    }

    public function getStreaks(Fixture $fixture, Request $request): array
    {
        $threshold = $request->get('threshold');

        return $this->parseStreaks($fixture->getStatistics(StatisticCornerType::values(), $threshold)->get(), $request);
    }
}
