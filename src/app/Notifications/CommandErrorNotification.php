<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommandErrorNotification extends Notification
{
    use Queueable;

    protected $commandName;
    protected $errorMessage;
    protected $stackTrace;

    public function __construct(string $commandName, string $errorMessage, string $stackTrace)
    {
        $this->commandName = $commandName;
        $this->errorMessage = $errorMessage;
        $this->stackTrace = $stackTrace;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject('Error in Command: ' . $this->commandName)
            ->line('An error occurred while running the command: ' . $this->commandName)
            ->line('Error: ' . $this->errorMessage)
            ->line('Stack Trace:')
            ->line($this->stackTrace);
    }
}
