<?php

namespace App\Traits;

use Illuminate\Support\Facades\{Cache, Date};
trait JHCacheTrait
{
    public function cached(string $method, array $args, $ttl = 1, $callable = null, bool $clearCache = false): mixed
    {
        /**
         * DISABLING CACHE FOR NOW
         */
        if (is_callable($callable)) {
            return call_user_func($callable);
        }
        return $callable;
        /*
        $key = [$method];

        foreach ($args as $arg) {
            $key[] = is_integer($arg) ? $arg : $arg->id;
        }

        $key = join('_', $key);

        if ($clearCache) {
            Cache::forget($key);
        }

        return Cache::remember(
            $key,
            Date::now()->addMinutes($ttl),
            function () use ($callable) {
                if (is_callable($callable)) {
                    return call_user_func($callable);
                }
                return $callable;
            }
        );
        */
    }
}
