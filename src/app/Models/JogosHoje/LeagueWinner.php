<?php

namespace App\Models\JogosHoje;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class LeagueWinner extends Model
{
    use HasFactory;

    protected $fillable = [
        'league_id',
        'team_id',
        'titles'
    ];

    protected $table = 'leagues_winners';

    public function league()
    {
        return $this->belongsTo(League::class);
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function leagueTeamsTitles(int $leagueId)
    {
        return self::join('teams', 'teams.id', '=', 'leagues_winners.team_id')
            ->join('countries', 'countries.id', '=', 'teams.country_id')
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'leagues_winners.league_id');
                $join->on('league_seasons.current', '<>', DB::raw("1"));
            })
            ->leftJoin('media_sites', 'media_sites.id', '=', 'teams.media_site_id')
            ->leftJoin('medias', 'medias.id', '=', 'media_sites.media_id')
            ->where('leagues_winners.league_id', $leagueId)
            ->groupBy('leagues_winners.team_id')
            ->orderBy('titles', 'desc')
            ->select([
                DB::raw("countries.id country_id"),
                DB::raw("countries.slug country_slug"),
                DB::raw("countries.name country_name"),
                DB::raw("teams.name"),
                DB::raw("teams.slug"),
                DB::raw("teams.media_site_id"),
                DB::raw("media_sites.width"),
                DB::raw("media_sites.height"),
                DB::raw("media_sites.filename"),
                DB::raw("media_sites.alt"),
                DB::raw("media_sites.id media_site_id"),
                DB::raw("medias.guid"),
                DB::raw("teams.id"),
                DB::raw("titles"),
                DB::raw("(SELECT IF(league_seasons.winner = teams.id, 1,0) FROM league_seasons JOIN seasons ON seasons.id = league_seasons.season_id WHERE ((league_seasons.current <> 1 and league_seasons.end > NOW()) OR (current = 1 AND league_seasons.end < NOW())) AND league_seasons.deleted_at IS NULL AND league_seasons.league_id = {$leagueId} ORDER BY league_seasons.id DESC LIMIT 1) winner_last_season"),
            ]);
    }
}
