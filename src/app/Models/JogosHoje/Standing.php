<?php

namespace App\Models\JogosHoje;

use App\Helpers\Utils;
use \Illuminate\Database\Eloquent\Builder;
use App\Models\GuiidyModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\{Model, SoftDeletes};
use Illuminate\Support\Facades\DB;
use Watson\Validating\ValidatingTrait;
use Illuminate\Http\{Request};

class Standing extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = 'standings';

    public $timestamps = false;

    protected $fillable = [
        'season_id',
        'league_id',
        'team_id',
        'round',
        'rank',
        'points',
        'goals_diff',
        'form',
        'group',
        'description',
        'status',
        'played',
        'wins',
        'draws',
        'loses',
        'goals_for',
        'goals_against',
        'last_updated',
        'extra',
        'color',
    ];

    protected ?array $rules = [
        'season_id' => 'required',
        'league_id' => 'required',
        'team_id' => 'required',
    ];

    private const int CURRENT_LEAGUE = 1;

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function league(): BelongsTo
    {
        return $this->belongsTo(League::class);
    }

    public function persistStandings(array $standings): void
    {
        foreach ($standings as $value) {
            self::updateOrCreate(
                [
                    'season_id' => $value['season_id'],
                    'league_id' => $value['league_id'],
                    'team_id' => $value['team_id'],
                    'group' => $value['group'],
                ],
                $value,
            );
        }
    }

    public function standingsForBothTeams(Fixture $fixture): Builder
    {
        $roundStr = null;
        if ($fixture->_round) {
            $roundStr = Utils::extractTitle($fixture->_round);
        }

        return self::with(['team'])
            ->where('standings.league_id', $fixture->league_id)
            ->where('standings.season_id', $fixture->season_id)
            /*
            ->where(function($q) use ($roundStr) {
                if ($roundStr) {
                    $q->whereRaw("standings.group LIKE '%{$roundStr}%'");
                }
            })
            */;
    }

    public function standingsForATeam(int $teamId, int $leagueId, int $seasonId): Builder
    {
        return self::where(function ($q) use ($leagueId) {
            $q->where('league_id', $leagueId);
        })
            ->where(function ($q) use ($seasonId) {
                $q->where('season_id', $seasonId);
            })
            ->whereIn("group", function ($q) use ($teamId, $seasonId, $leagueId) {
                $q->select("group")
                    ->from('standings')
                    ->where(function ($q) use ($leagueId) {
                        $q->where('league_id', $leagueId);
                    })
                    ->where(function ($q) use ($seasonId) {
                        $q->where('season_id', $seasonId);
                    })
                    ->whereNull('deleted_at')
                    ->groupBy("group")
                    ->havingRaw("GROUP_CONCAT(IF(team_id = {$teamId},team_id, NULL)) IS NOT NULL");
            })
            ->orderBy('rank', 'asc');
    }

    public function standingsForALeague(int $leagueId, Request $request): Builder
    {
        $seasonId = $request->get('season_id');
        $specificRound = $request->get('round');

        return self::select(
            'standings.rank',
            'standings.points',
            'standings.season_id',
            'standings.played',
            'standings.wins',
            'standings.description',
            'standings.draws',
            'standings.loses',
            'standings.team_id',
            'standings.group',
            'standings.extra',
            'standings.color'
        )
            ->join('seasons', 'standings.season_id', '=', 'seasons.id')
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.current', DB::raw(self::CURRENT_LEAGUE))
                    ->on('league_seasons.league_id', '=', 'standings.league_id');
            })
            ->join('leagues', 'leagues.id', '=', 'league_seasons.league_id')
            ->join('teams', 'teams.id', '=', 'standings.team_id')
            ->where('standings.league_id', $leagueId)
            ->where(function ($q) use ($seasonId) {
                if ($seasonId) {
                    $q->where('standings.season_id', $seasonId);
                } else {
                    $q->whereRaw('standings.season_id = league_seasons.season_id');
                }
            })
            ->where(function ($q) use ($specificRound) {
                if ($specificRound) {
                    $q->whereRaw("standings.group LIKE '%{$specificRound}%'");
                }
            })
            //->with(['team'])
            ->orderByRaw('standings.points desc, standings.rank asc')
            ->groupBy('standings.team_id');
    }
}
