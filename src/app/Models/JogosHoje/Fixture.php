<?php

namespace App\Models\JogosHoje;

use App\Builders\FixturesBuilder;
use App\Enums\JogosHoje\{Betometer, HomeAwayType};
use App\Models\{GuiidyModel, User};
use App\Services\JogosHoje\ImagePathService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\{BelongsTo, HasMany, HasOne, MorphMany};
use Illuminate\Database\Eloquent\{Builder, Collection as EloquentCollection, Model, SoftDeletes};
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Watson\Validating\ValidatingTrait;

class Fixture extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    public $timestamps = false;

    protected $fillable = [
        'api_id',
        'date',
        'league_id',
        'team_home',
        'team_away',
        'round',
        'status',
        'elapsed',
        'is_live',
        'winner',
        'no_events',
        'goals_home',
        'goals_away',
        'goals_extra_home',
        'goals_extra_away',
        'penalty_home',
        'penalty_away',
        'last_updated',
        'no_stadings',
        'broadcasters',
        'venue_id',
        'referee_id',
        'season_id',
        'the_sports_id',
        'environment',
        'cards_home',
        'cards_away',
        'corners_home',
        'corners_away',
        'has_predictions',
        'has_lineups',
        'goals_home_ht',
        'goals_away_ht',
        'extra_1H',
        'extra_2H',
        '_round',
        'finished_at',
    ];

    protected ?array $rules = [
        'api_id' => 'required',
    ];

    protected $casts = [
        'is_live' => 'boolean',
        'no_events' => 'boolean',
        'no_stadings' => 'boolean',
        'broadcasters' => 'array',
        'environment' => 'array',
        'has_predictions' => 'boolean',
        'has_lineups' => 'boolean',
    ];

    public const string TIME_TO_BE_DEFINED = 'TBD';
    public const string NOT_STARTED = 'NS';
    public const string FIRST_HALF = '1H';
    public const string HALF_TIME = 'HT';
    public const string SECOND_HALF = '2H';
    public const string EXTRA_TIME = 'ET';
    public const string BREAK_TIME = 'BT';
    public const string PENALTY_IN_PROGRESS = 'P';
    public const string MATCH_SUSPENDED = 'SUSP';
    public const string MATCH_INTERRUPTED = 'INT';
    public const string MATCH_FINISHED = 'FT';
    public const string MATCH_FINISHED_EXTRA_TIME = 'AET';
    public const string MATCH_FINISHED_PENALTY_SHOOTOUT = 'PEN';
    public const string MATCH_POSTPONED = 'PST';
    public const string MATCH_CANCELED = 'CANC';
    public const string MATCH_ABANDONED = 'ABD';
    public const string TECHNICAL_LOSS = 'AWD';
    public const string WALKOVER = 'WO';
    public const string IN_PROGRESS = 'LIVE';

    public function newEloquentBuilder($query): FixturesBuilder
    {
        return new FixturesBuilder($query);
    }

    public static function isLive($shortType): bool
    {
        return in_array($shortType, [
            self::IN_PROGRESS,
            self::FIRST_HALF,
            self::HALF_TIME,
            self::SECOND_HALF,
            self::EXTRA_TIME,
            self::BREAK_TIME,
            self::PENALTY_IN_PROGRESS,
                //self::MATCH_SUSPENDED,
            self::MATCH_INTERRUPTED,
        ]);
    }

    public static function isFinished($shortType): bool
    {
        return in_array($shortType, [
            self::MATCH_FINISHED,
            self::MATCH_FINISHED_EXTRA_TIME,
            self::MATCH_FINISHED_PENALTY_SHOOTOUT,
            self::MATCH_ABANDONED,
            self::MATCH_CANCELED,
            self::TECHNICAL_LOSS,
            self::WALKOVER,
        ]);
    }

    public function fixtureEvents(): HasMany
    {
        return $this->hasMany(FixtureEvent::class)->with(['team'])->orderBy(
            'id',
            'desc'
        );
    }

    public function fixtureStatistics(): HasMany
    {
        return $this->hasMany(FixtureStatistic::class)->with(['team']);
    }

    public function homeTeam(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'team_home')->with(['country']);
    }

    public function awayTeam()
    {
        return $this->belongsTo(Team::class, 'team_away')->with(['country']);
    }

    public function league()
    {
        return $this->belongsTo(League::class)->with(['country']);
    }

    public function toArray($includeTeams = true): array
    {
        $data = parent::toArray();

        if ($includeTeams) {
            $this->setTeamData($this->homeTeam, 'home', $data);
            $this->setTeamData($this->awayTeam, 'away', $data);
        }
        return $data;
    }

    private function setTeamData($team, $identifier, &$data): void
    {
        $data[$identifier . '_team'] = [
            'id' => $team?->id,
            'api_id' => $team?->api_id,
            'name' => $team?->name,
            'slug' => $team?->slug,
            'country_slug' => isset($team->country) ? $team->country->slug : null,
            'image_url' => ImagePathService::get($team),
        ];
    }

    public static function isFuture($shortType): bool
    {
        switch ($shortType) {
            case self::TIME_TO_BE_DEFINED:
            case self::NOT_STARTED:
            case self::MATCH_POSTPONED:
                $isFuture = true;
                break;

            default:
                $isFuture = false;
        }

        return $isFuture;
    }

    public function getFixtures(array $fields = [], array $where = []): Builder
    {
        $builder = self::select($fields);

        if (!empty($where)) {
            $builder->where($where);
        }
        return $builder;
    }

    public function filterDailyFixtures(Builder $builder): Builder
    {
        $date = date('Y-m-d', strtotime('yesterday'));

        return $builder
            ->whereRaw("DATE(date) = '{$date}'")
            //->whereDoesntHave('fixtureEvents')
            ->whereNotIn('status', [
                self::TIME_TO_BE_DEFINED,
                self::NOT_STARTED,
                self::MATCH_POSTPONED,
            ])
            //->where('no_events', false)
            ->orderBy('id', 'desc');
    }

    public function persistFixtures(array $fixtures): void
    {
        foreach ($fixtures as $value) {
            self::updateOrCreate(
                ['api_id' => $value['api_id']],
                $value
            );

            $fixture = self::where('api_id', $value['api_id'])->first();

            if (!$fixture->finished_at && self::isFinished($fixture->status)) {
                $fixture->update(['finished_at' => now()->format('Y-m-d H:i:s')]);
            }
        }
    }

    public function getSeasonAndLeagues(array $criteria): Builder
    {
        return $this->join('league_seasons', 'league_seasons.league_id', '=', 'fixtures.league_id')
            ->join('leagues', 'leagues.id', '=', 'league_seasons.league_id')
            ->join('seasons', 'seasons.id', '=', 'league_seasons.season_id')
            ->select('leagues.api_id AS league', 'seasons.season AS season')
            ->groupBy('leagues.api_id', 'seasons.season')
            ->where($criteria);
    }

    public function fetchFixturesWithoutLineups(int $fixture): Builder
    {
        if ($fixture) {
            return $this->whereId($fixture);
        }
        return $this->whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                ->from('fixture_lineups')
                ->join(
                    'fixture_lineup_team_formations',
                    'fixture_lineup_team_formations.lineup_id',
                    '=',
                    'fixture_lineups.id'
                )
                ->groupBy('fixture_lineups.id')
                ->whereColumn('fixture_id', 'fixtures.id');
        })->whereBetween('date', [Carbon::yesterday(), Carbon::tomorrow()]);
    }

    public function withoutPlayerInjuries(): Builder
    {
        return $this->join('player_injuries', 'player_injuries.fixture_id', '=', 'fixtures.id')
            ->where(function ($query) {
                $query->where('player_injuries.injury_type', PlayerInjury::QUESTIONABLE_INJURY)
                    ->orWhereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('player_injuries')
                            ->whereColumn('player_injuries.fixture_id', 'fixtures.id');
                    });
            })
            ->whereDate('date', Carbon::today());
    }

    public function getFixturesForPredictions(array $leagueIds): Builder
    {
        return $this->whereDate('date', '>=', Carbon::now())->whereIn('league_id', $leagueIds);
    }

    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class, 'venue_id');
    }

    public function referee(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee_id');
    }

    public function findByApiId(string|int $apiId): ?Builder
    {
        return self::where('api_id', $apiId);
    }

    public function prediction(): HasOne
    {
        return $this->hasOne(FixturePrediction::class, 'fixture_id');
    }

    public function userFavoritefixtures(array $userFavorites, string $tzOffset): Collection
    {
        $leagueIds = $userFavorites['leagues']->pluck('id')->toArray();
        $teamIds = $userFavorites['teams']->pluck('id')->toArray();

        if (sizeof($leagueIds) == 0 && sizeof($teamIds) == 0) {
            return collect([]);
        }

        return $this
            ->handleLeaguesRelation()
            ->where(function ($q) use ($leagueIds, $teamIds) {
                $q->where(function ($q1) use ($leagueIds) {
                    if (count($leagueIds)) {
                        $q1->whereIn('fixtures.league_id', $leagueIds);
                    }
                })
                    ->orWhere(function ($q2) use ($teamIds) {
                        if (sizeof($teamIds)) {
                            $q2->where(function ($query) use ($teamIds) {
                                $query->whereIn('team_home', $teamIds);
                            })->orWhere(function ($query) use ($teamIds) {
                                $query->whereIn('team_away', $teamIds);
                            });
                        }
                    });
            })
            ->whereRaw("DATE(CONVERT_TZ(date, '+00:00', '$tzOffset')) = DATE(date)")
            ->select([DB::raw('DATE(fixtures.date) as date'), DB::raw("count(*) num")])
            ->groupBy(DB::raw('DATE(fixtures.date)'))
            ->orderBy('fixtures.date')
            ->havingRaw('num > 0')
            ->get();
    }

    public function fixturesBetweenDates(string $start, string $end): Builder
    {
        return $this->whereBetween('date', [$start, $end])
            ->handleLeaguesRelation()            
            ->select([
                DB::raw('DATE(fixtures.date) as date'), 
                DB::raw("count(*) num"),
            ])
            ->groupBy(DB::raw('DATE(fixtures.date)'))
            ->orderBy('fixtures.date')
            ->havingRaw('num > 0');
    }

    public function getFixturesLimitsAndOffsetsForADay(Request $request, array $fixturesIds): EloquentCollection
    {
        return self::handleLeaguesRelation()
            ->handleCountryLeagueRelation($request)
            ->handleRegionLeagueRelation($request)
            ->handleIsLiveCondition($request)
            ->handleLeaguesCondition($request)
            ->whereIn('fixtures.id', $fixturesIds)
            ->handleOrder(true)
            ->select(['fixtures.league_id', 'fixtures.id'])
            ->groupBy('fixtures.id')
            ->get();
    }

    public function fixturesLimitsAndOffsetsForADay(Request $request, string $date, string $tzOffset, array $leaguesInUseIds): EloquentCollection
    {
        return self::handleLeaguesRelation()
            ->handleCountryLeagueRelation($request)
            ->handleRegionLeagueRelation($request)
            ->handleIsLiveOrDayCondition($request, $date, $tzOffset)
            ->handleLeaguesCondition($request)
            ->whereIn('fixtures.league_id', $leaguesInUseIds)
            ->handleOrder(true)
            ->select([
                'fixtures.league_id',
                'fixtures.season_id',
                'fixtures.id'
            ])
            ->groupBy('fixtures.id')
            ->get();
    }    

    public function getFixturesForADay(Request $request, int $offset, int $limit, array $fixturesIds): Builder
    {
        return self::with(['homeTeam', 'awayTeam', 'league', 'predictions'])
            ->handleLeaguesRelation()
            ->handleCountryLeagueRelation($request)
            ->handleRegionLeagueRelation($request)
            ->handleIsLiveCondition($request)
            ->handleLeaguesCondition($request)
            ->whereIn('fixtures.id', $fixturesIds)
            ->handleOrder()
            ->select([
                'fixtures.*',
                DB::raw('fixtures.id as id'),
                DB::raw('fixtures.the_sports_id as the_sports_id'),
                DB::raw('fixtures.league_id')
            ])
            ->offset($offset)
            ->limit($limit)
            ->groupBy('fixtures.id');
    }

    public function fixturesForADay(Request $request, string $date, string $tzOffset, int $offset, int $limit, ?array $ids = []): Builder
    {
        return self::with(['homeTeam', 'awayTeam', 'league', 'predictions'])
            ->handleLeaguesRelation()
            ->handleCountryLeagueRelation($request)
            ->handleRegionLeagueRelation($request)
            ->handleIsLiveOrDayCondition($request, $date, $tzOffset)
            ->handleLeaguesCondition($request)
            ->whereIn('fixtures.id', $ids)
            ->handleOrder()
            ->select([
                'fixtures.*',
                DB::raw('fixtures.id as id'),
                DB::raw('fixtures.the_sports_id as the_sports_id'),
                DB::raw('fixtures.league_id')
            ])
            ->offset($offset)
            ->limit($limit)
            ->groupBy('fixtures.id');
    }

    public function upcomingFixtures(int $leagueId, string $tzOffset, string $date, int $limit = 10): EloquentCollection
    {
        return self::with(['homeTeam', 'awayTeam', 'league', 'predictions'])
            ->handleLeaguesRelation()
            ->whereRaw("DATE(CONVERT_TZ(fixtures.date, '+00:00', ?)) > ?", [$tzOffset, $date])
            ->where('fixtures.league_id', $leagueId)
            ->orderBy('has_predictions', 'DESC')
            ->orderBy('date')
            ->select([
                'fixtures.*',
                DB::raw('fixtures.id as id'),
                DB::raw('fixtures.the_sports_id as the_sports_id'),
                DB::raw('fixtures.league_id')
            ])
            ->offset(0)
            ->limit($limit)
            ->get();
    }

    public function fixturesOfBothTeam(Fixture $fixture): Builder
    {
        return self::with(['homeTeam', 'awayTeam', 'league'])
            ->join('league_seasons', 'league_seasons.league_id', '=', 'fixtures.league_id')
            ->join('seasons', 'seasons.id', '=', 'league_seasons.season_id')
            ->where('league_seasons.current', true)
            ->where(function ($q) use ($fixture) {
                $q->whereIn('team_home', [$fixture->team_home, $fixture->team_away])
                    ->orWhereIn('team_away', [$fixture->team_home, $fixture->team_away]);
            })
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->orderBy('date', 'desc')
            ->select(['fixtures.*']);
    }

    public function avgGoalsForATeam(int $teamId, int $homeAway, ?int $leagueId = null): mixed
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";
        $homeAwayCondition = "(({$homeAway} = " . HomeAwayType::ALL_FIXTURES->value . " AND (team_home = {$teamId} OR team_away = {$teamId}))
                OR ({$homeAway} = " . HomeAwayType::HOME_TEAM_FIXTURES->value . " AND team_home = {$teamId})
                OR ({$homeAway} = " . HomeAwayType::AWAY_TEAM_FIXTURES->value . " AND team_away = {$teamId}))";

        $query = "
            SELECT
                SUM(games_home) games_home,
                SUM(games_away) games_away,
                MAX(goals_total_team_playing_home) max_goals_home,
                ROUND((SUM(goals_total_team_playing_home) / SUM(games_home)),2) avg_goals_home,
                MAX(goals_total_team_playing_away) max_goals_away,
                ROUND((SUM(goals_total_team_playing_away) / SUM(games_away)),2) avg_goals_away,
                MAX(goals_scored_by_team_playing_home) max_goals_scored_home,
                ROUND((SUM(goals_scored_by_team_playing_home) / SUM(games_home)),2) avg_goals_scored_by_match_home,
                MAX(goals_scored_by_team_playing_away) max_goals_scored_away,
                ROUND((SUM(goals_scored_by_team_playing_away) / SUM(games_away)),2) avg_goals_scored_by_match_away,
                MAX(goals_conceded_by_team_playing_home) max_goals_conceded_home,
                ROUND((SUM(goals_conceded_by_team_playing_home) / SUM(games_home)),2) avg_goals_conceded_by_match_home,
                MAX(goals_conceded_by_team_playing_away) max_goals_conceded_away,
                ROUND((SUM(goals_conceded_by_team_playing_away) / SUM(games_away)),2) avg_goals_conceded_by_match_away
            FROM (
                SELECT
                    COALESCE(IF(f.team_home = {$teamId}, 1,0), 0) games_home,
                    COALESCE(IF(f.team_home <> {$teamId}, 1,0), 0) games_away,
                    COALESCE(IF(f.team_home = {$teamId}, COALESCE(f.goals_home + f.goals_away, 0), 0), 0) goals_total_team_playing_home,
                    COALESCE(IF(f.team_home <> {$teamId}, COALESCE(f.goals_home + f.goals_away, 0), 0), 0) goals_total_team_playing_away,
                    COALESCE(IF(f.team_home = {$teamId}, COALESCE(f.goals_home,0), 0), 0) goals_scored_by_team_playing_home,
                    COALESCE(IF(f.team_home <> {$teamId}, COALESCE(f.goals_away,0),0), 0) goals_scored_by_team_playing_away,
                    COALESCE(IF(f.team_home = {$teamId}, COALESCE(f.goals_away,0),0), 0) goals_conceded_by_team_playing_home,
                    COALESCE(IF(f.team_home <> {$teamId}, COALESCE(f.goals_home,0),0), 0) goals_conceded_by_team_playing_away
                FROM fixtures f
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                    {$homeAwayCondition} AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.season_id = ls.season_id AND
                    f.deleted_at IS NULL
                    {$leagueCondition}
                GROUP BY f.id
            ) x;
        ";

        return DB::selectOne($query);
    }

    public function teamPerformance(int $teamId, int $limit = 10): Builder
    {
        return self::join('league_seasons', function ($join) {
            $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
            $join->on('league_seasons.current', '=', DB::raw("1"));
        })
            ->where(function (Builder $q) use ($teamId) {
                $q->where('team_home', $teamId)
                    ->orWhere('team_away', $teamId);
            })
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->whereRaw("fixtures.season_id = league_seasons.season_id")
            ->orderBy('date', 'DESC')
            ->limit($limit)
            ->groupBy('fixtures.id')
            ->select([
                DB::raw("fixtures.id id"),
                DB::raw("fixtures.date date"),
                DB::raw(
                    "IF((team_home = {$teamId} AND fixtures.winner = 'H') OR (team_away = {$teamId} AND fixtures.winner = 'A'), 'green', IF (fixtures.winner IS NOT NULL, 'red', NULL)) bar_color"
                ),
                DB::raw(
                    "IF((team_home = {$teamId} AND fixtures.winner = 'H') OR (team_away = {$teamId} AND fixtures.winner = 'A'), 1, IF (fixtures.winner IS NOT NULL, -1, 0)) * ABS((COALESCE(goals_home,0) - COALESCE(goals_away,0))) bar_height"
                ),
                DB::raw("goals_home"),
                DB::raw("goals_away"),
                DB::raw("team_home"),
                DB::raw("team_away"),
                DB::raw("fixtures.winner"),
            ]);
    }

    public function lastTeamFixtures(int $teamId, ?Request $request = null, int $limit = 10): Builder
    {
        return $this
            ->where(function (Builder $q) use ($teamId) {
                $q->where('team_away', $teamId)->orWhere('team_home', $teamId);
            })
            ->when($request && $request->get('league_id'), fn($q) => $q->where('fixtures.league_id', $request->get('league_id')))
            ->orderBy('date', 'DESC')
            ->limit($limit);
    }

    public function nextTeamFixtures(
        int $teamId,
        ?bool $featuredMatch = false,
        ?int $limit = 5,
        ?Request $request = null
    ): Builder {
        $currentOffset = (int) $request?->get('offset', 0);
        $absOffset = $currentOffset == 0 ? 0 : ($currentOffset < 0 ? abs($currentOffset) - 1 : abs($currentOffset));
        $offset = $absOffset * $limit;
        $direction = (!$request || ($request->get('offset', 0) >= 0)) ? 'ASC' : 'DESC';

        $query = $this
            ->where(function (Builder $q) use ($teamId) {
                $q->where('team_away', $teamId)->orWhere('team_home', $teamId);
            })
            ->when($featuredMatch, fn(Builder $q) => $q->whereRaw("date >= NOW()")->orWhereRaw("(NOW() BETWEEN fixtures.date AND DATE_ADD(fixtures.date, INTERVAL 12 HOUR))"))
            ->when($request && $request->get('league_id'), fn($q) => $q->where('fixtures.league_id', $request->get('league_id')))
            ->orderBy('date', $direction)
            ->where(function ($q) use ($direction) {
                $q->whereRaw($direction === 'ASC' 
                    ? "date >= NOW()" 
                    : "date < NOW()");
            })
            ->limit($limit)
            ->offset($offset);

        if ($request !== null) {
            $query->when(true, function ($baseQuery) use ($request, $teamId) {
                $maxDate = (clone $baseQuery)->get()->max('date');
                $minDate = (clone $baseQuery)->get()->min('date');

                $basePaginationQuery = self::where(function (Builder $q) use ($teamId) {
                    $q->where('team_away', $teamId)
                        ->orWhere('team_home', $teamId);
                    })
                    ->when($request->get('league_id'), fn ($query) => $query->where('fixtures.league_id', $request->get('league_id')));

                if ($maxDate) {
                    $hasNext = (clone $basePaginationQuery)
                        ->where('date', '>', $maxDate)
                        ->exists();

                    $request->attributes->set('has_next', $hasNext);
                }

                if ($minDate) {
                    $hasPrevious = (clone $basePaginationQuery)
                        ->where('date', '<', $minDate)
                        ->exists();

                    $request->attributes->set('has_previous', $hasPrevious);
                }
            });
        }

        return $query;
    }

    public function maxGoalsForTeams(string $dateTime, int $leagueId): ?float
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                MAX(f.goals_home + f.goals_away) max
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                f.deleted_at IS NULL AND
                f.season_id = ls.season_id AND
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') and
                DATE(f.date) < DATE(?)
                $leagueCondition
            ;
        ";

        return DB::selectOne($query, [$dateTime])->max ?? null;
    }

    public function avgGoalsForTeams(
        int $homeTeam,
        int $awayTeam,
        string $type,
        string $dateTime,
        ?int $leagueId = null,
        ?int $homeAway = 0
    ): Collection {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        switch ($type) {
            case 'in_favor':
                $conditionHome = join(',', [
                    "(SUM(IF(f.team_home = {$homeTeam},f.goals_home,0)) + SUM(IF(f.team_away = {$homeTeam},f.goals_away,0))) total_{$type}_goals",
                    "((SUM(IF(f.team_home = {$homeTeam},f.goals_home,0)) + SUM(IF(f.team_away = {$homeTeam},f.goals_away,0))) / SUM(1)) avg",
                ]);

                $conditionAway = join(',', [
                    "(SUM(IF(f.team_home = {$awayTeam},f.goals_home,0)) + SUM(IF(f.team_away = {$awayTeam},f.goals_away,0))) total_{$type}_goals",
                    "((SUM(IF(f.team_home = {$awayTeam},f.goals_home,0)) + SUM(IF(f.team_away = {$awayTeam},f.goals_away,0))) / SUM(1)) avg",
                ]);

                break;
            case 'against':
                $conditionHome = join(',', [
                    "(SUM(IF(f.team_home = {$homeTeam},f.goals_away,0)) + SUM(IF(f.team_away = {$homeTeam},f.goals_home,0))) total_{$type}_goals",
                    "((SUM(IF(f.team_home = {$homeTeam},f.goals_away,0)) + SUM(IF(f.team_away = {$homeTeam},f.goals_home,0))) / SUM(1)) avg",
                ]);

                $conditionAway = join(',', [
                    "(SUM(IF(f.team_home = {$awayTeam},f.goals_away,0)) + SUM(IF(f.team_away = {$awayTeam},f.goals_home,0))) total_{$type}_goals",
                    "((SUM(IF(f.team_home = {$awayTeam},f.goals_away,0)) + SUM(IF(f.team_away = {$awayTeam},f.goals_home,0))) / SUM(1)) avg",
                ]);

                break;
            default:
                $conditionHome = join(',', [
                    "(SUM(f.goals_home) + SUM(f.goals_away)) {$type}_goals",
                    "((SUM(f.goals_home) + SUM(f.goals_away)) / SUM(1)) avg",
                ]);

                $conditionAway = join(',', [
                    "(SUM(f.goals_home) + SUM(f.goals_away)) {$type}_goals",
                    "((SUM(f.goals_home) + SUM(f.goals_away)) / SUM(1)) avg",
                ]);

                break;
        }

        $query = "
            SELECT
                GROUP_CONCAT(DISTINCT(t.name)) team_name,
                SUM(1) total_games,
                {$conditionHome}
            FROM fixtures f
            JOIN teams t ON t.id = ?
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                ((? <> 1 AND (f.team_home = ? or f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND
                f.season_id = ls.season_id AND
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL AND
                DATE(f.date) < DATE(?)
                {$leagueCondition}
            UNION ALL
            SELECT
                GROUP_CONCAT(DISTINCT(t.name)) team_name,
                SUM(1) total_games,
                {$conditionAway}
            FROM fixtures f
            JOIN teams t ON t.id = ?
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                ((? <> 1 AND (f.team_home = ? or f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                f.season_id = ls.season_id AND
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL AND
                DATE(f.date) < DATE(?)
                {$leagueCondition}
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function nextLeagueFixtures(int $leagueId, Request $request): Builder
    {
        $round = $request->get('_round');
        $seasonId = $request->get('season_id');
        $roundCondition = $round ? " AND f._round = '{$round}'" : "";

        $query = "
            SELECT * FROM (
                SELECT
                    MIN(f.date) min_date,
                    MAX(f.date) max_date,
                    DATE(DATE_ADD(NOW(), INTERVAL 2 DAY)) max_date_2_days_later,
                    GROUP_CONCAT(f.id) fixtures_ids,
                    ROUND(SUM(IF(f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'),1,0)),0) finished_games,
                    ROUND(SUM(1),0) total_games,
                    f._round
                FROM fixtures f
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    f.league_id = ? AND
                    f.deleted_at IS NULL AND
                    f._round IS NOT NULL AND
                    f.season_id = " . ($seasonId ?? 'ls.season_id') . "
                    {$roundCondition}
                GROUP BY f._round
                ORDER BY f.date
            ) x
            ORDER BY x.min_date ASC;
        ";

        $rounds = collect(
            DB::select($query, [$leagueId])
        );

        $ids = [];

        foreach ($rounds as $index => $round) {
            if (
                (($round->finished_games <= $round->total_games) && ((strtotime($round->min_date) <= strtotime('now')) && (strtotime('now') <= strtotime($round->max_date_2_days_later)))) // current round
                ||
                ($round->finished_games == 0) // next round
                ||
                ($index == ($rounds->count() - 1)) // last round of a season
            ) {
                $ids = explode(',', $round->fixtures_ids);
                break;
            }
        }

        return self::whereIn('id', $ids)->orderBy('date');
    }

    public function goalsCardsAndCornersStatsForALeague(int $leagueId, ?int $seasonId = null): Collection
    {
        $query = "
            SELECT
                t.name,
                t.slug,
                ms.width,
                ms.height,
                ms.filename,
                ms.alt alt,
                ms.id media_site_id,
                m.guid,
                x.team_id,
                SUM(IF(x.total_goals > 1,1,0)) over15,
                SUM(IF(x.total_goals > 2,1,0)) over25,
                SUM(IF(x.btts = 1,1,0)) btts,
                SUM(x.total_goals) goals,
                SUM(1) games,
                ROUND(((SUM(IF(x.total_goals > 1,1,0)) / SUM(1)) * 100),2) over15_percent,
                ROUND(((SUM(IF(x.total_goals > 2,1,0)) / SUM(1)) * 100),2) over25_percent,
                ROUND(((SUM(IF(x.btts = 1,1,0)) / SUM(1)) * 100),2) btts_percent,
                ROUND((SUM(x.total_goals) / SUM(1)),2) goals_match,
                SUM(IF(x.cards > 4,1,0)) cards_over45,
                SUM(IF(x.corners <= 10,1,0)) corners_under105,
                ROUND(((SUM(IF(x.cards > 4,1,0)) / SUM(1)) * 100),2) cards_over45_percent,
                ROUND(((SUM(IF(x.corners <= 10,1,0)) / SUM(1)) * 100),2) corners_under105_percent
            FROM (
                SELECT
                    f.team_home team_id,
                    f.goals_home goals,
                    f.cards_home cards,
                    f.corners_home corners,
                    IF(f.goals_home > 0 AND f.goals_away > 0,1,0) btts,
                    (f.goals_home + f.goals_away) total_goals,
                    (f.cards_home + f.cards_away) total_cards,
                    (f.corners_home + f.corners_away) total_corners
                FROM fixtures f
                JOIN leagues l ON l.id = f.league_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    f.league_id = ? AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND
                    f.season_id = " . ($seasonId ?? 'ls.season_id') . "
                GROUP BY f.id
                UNION ALL
                SELECT
                    f.team_away team_id,
                    f.goals_away goals,
                    f.cards_away cards,
                    f.corners_away corners,
                    IF(f.goals_home > 0 AND f.goals_away > 0,1,0) btts,
                    (f.goals_home + f.goals_away) total_goals,
                    (f.cards_home + f.cards_away) total_cards,
                    (f.corners_home + f.corners_away) total_corners
                FROM fixtures f
                JOIN leagues l ON l.id = f.league_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    f.league_id = ? AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND
                    f.season_id = " . ($seasonId ?? 'ls.season_id') . "
                GROUP BY f.id
            ) x
            JOIN teams t on t.id = x.team_id
            LEFT JOIN media_sites ms on ms.id = t.media_site_id
            LEFT JOIN medias m on m.id = ms.media_id
            GROUP BY x.team_id;
        ";

        return collect(
            DB::select($query, [$leagueId, $leagueId])
        );
    }

    public function season(): BelongsTo
    {
        return $this->belongsTo(Season::class);
    }

    public function cacheTtl(): int
    {
        $ttl = 1; // 1 minute

        switch ($this->attributes['status']) {
            case self::TIME_TO_BE_DEFINED:
            case self::NOT_STARTED:
                $now = Carbon::now();
                $oneHourThirtyBeforeStart = Carbon::parse($this->attributes['date'])
                    ->subHours(1)
                    ->subMinutes(30);
                $ttl = $now->diffInMinutes($oneHourThirtyBeforeStart, false);
                $ttl = $ttl < 0 ? 10 : $ttl;
                break;
            case self::MATCH_POSTPONED:
                $ttl = 1440; // 1 day
                break;
            case self::MATCH_FINISHED:
            case self::MATCH_FINISHED_EXTRA_TIME:
            case self::MATCH_FINISHED_PENALTY_SHOOTOUT:
            case self::MATCH_ABANDONED:
            case self::MATCH_CANCELED:
            case self::WALKOVER:
            case self::TECHNICAL_LOSS:
                if ((strtotime($this->attributes['date']) + 2880) > strtotime('now')) { // 2880 = 2 days
                    $ttl = 120; // 2 hours
                    break;
                }

                $ttl = 43200; // 30 days
                break;
            default:
                break;
        }

        return $ttl;
    }

    public function markets(): HasMany
    {
        return $this->hasMany(Market::class);
    }

    public function lineups(): HasMany
    {
        return $this->hasMany(FixtureLineup::class);
    }

    public function getH2H(int $homeTeamId, int $awayTeamId, string $dateTime, ?int $teamId, ?int $leagueId): Builder
    {
        $date = date('Y-m-d', strtotime($dateTime));

        $queryBuilder = $this->select([
            'fixtures.id as fixture_id',
            'fixtures.date',
            'fixtures.round',
            'fixtures.status',
            'fixtures.team_home',
            'fixtures.team_away',
            'fixtures.winner',
            'fixtures.goals_home',
            'fixtures.goals_away',
            'fixtures.goals_extra_home',
            'fixtures.goals_extra_away',
            'fixtures.penalty_home',
            'fixtures.penalty_away',
            'c.name as country_name',
            'c.id as country_id',
            'leagues.name as league_name',
            'leagues.id as league_id',
        ])
            ->join('leagues', 'leagues.id', '=', 'fixtures.league_id')
            ->join('countries as c', 'c.id', '=', 'leagues.country_id')
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->where(function ($q) use ($homeTeamId, $awayTeamId, $teamId) {
                if (is_null($teamId)) {
                    $q->where(function ($q1) use ($homeTeamId, $awayTeamId) {
                        $q1->where('fixtures.team_home', $homeTeamId)
                            ->where('fixtures.team_away', $awayTeamId);
                    })
                        ->orWhere(function ($q2) use ($homeTeamId, $awayTeamId) {
                            $q2->where('fixtures.team_home', $awayTeamId)
                                ->where('fixtures.team_away', $homeTeamId);
                        });
                } else {
                    $q->where(function ($q1) use ($teamId) {
                        $q1->where('fixtures.team_home', $teamId)
                            ->orWhere('fixtures.team_away', $teamId);
                    })
                        ->whereRaw('fixtures.season_id = league_seasons.season_id');
                }
            })
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->whereRaw("DATE(fixtures.date) < '{$date}'")
            ->groupBy('fixtures.id')
            ->orderBy('fixtures.date', 'DESC');

        if ($leagueId) {
            $queryBuilder->where('fixtures.league_id', $leagueId);
        }

        return $queryBuilder;
    }

    public function addPagination(Builder $query, $offset): Builder
    {
        return $query->offset($offset);
    }

    public function getMatchGoals(int $teamId, ?int $leagueId)
    {
        $queryBuilder = $this->select([
            'fixtures.id',
            'fixtures.date',
            'fixtures.team_home',
            'fixtures.team_away',
            'fixture_events.time',
            DB::raw('SUM(CASE WHEN fixture_events.time <= 45 AND fixture_events.team_id = fixtures.team_home THEN 1 ELSE 0 END) AS home_team_first_half_goals'),
            DB::raw('SUM(CASE WHEN fixture_events.time <= 45 AND fixture_events.team_id = fixtures.team_away THEN 1 ELSE 0 END) AS away_team_first_half_goals'),
            DB::raw('SUM(CASE WHEN fixture_events.time > 45 AND fixture_events.team_id = fixtures.team_home THEN 1 ELSE 0 END) AS home_team_second_half_goals'),
            DB::raw('SUM(CASE WHEN fixture_events.time > 45 AND fixture_events.team_id = fixtures.team_away THEN 1 ELSE 0 END) AS away_team_second_half_goals')
        ])
            ->join('fixture_events', 'fixtures.id', '=', 'fixture_events.fixture_id')
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->where(function ($query) use ($teamId) {
                $query->where('fixtures.team_home', $teamId)
                    ->orWhere('fixtures.team_away', $teamId);
            });

        if ($leagueId) {
            $queryBuilder->where('fixtures.league_id', $leagueId);
        }

        return $queryBuilder->where('fixture_events.type', '=', 'goal')->groupBy(
            'fixtures.id',
            'fixtures.date',
            'fixtures.team_home',
            'fixtures.team_away'
        )
            ->orderBy('fixtures.date');
    }

    public function getTeamMatches(int $teamId): Builder
    {
        return self::getCurrentLeague()
            ->where(function ($q) use ($teamId) {
                $q->where('team_home', $teamId)
                    ->orWhere('team_away', $teamId);
            })
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->orderBy('date', 'desc')
            ->with(['homeTeam', 'awayTeam', 'league'])
            ->select([
                'fixtures.winner',
                'fixtures.league_id',
                'fixtures.team_home',
                'fixtures.team_away',
            ]);
    }

    public function getStatistics(array $values, ?string $threshold): HasMany
    {
        $queryBuilder = $this->hasMany(Statistic::class)->with(
            ['statisticDetailOne.fixturesInStreak', 'statisticDetailTwo.fixturesInStreak']
        )->whereHas('statisticDetailOne', function ($query) use ($values) {
            $query->whereIn('config_type_id', $values);
        });

        if (!empty($threshold)) {
            $queryBuilder->whereHas('statisticDetailOne', function ($query) use ($threshold) {
                $query->where('config_threshold', $threshold);
            });
        }
        return $queryBuilder;
    }

    public function getTeamNextMatch(int $teamId)
    {
        return Fixture::where(function ($query) use ($teamId) {
            $query->where('team_home', $teamId)
                ->orWhere('team_away', $teamId);
        })
            ->where('date', '>', now())
            ->orderBy('date', 'asc')
            ->first();
    }

    public function getTeamStreaks(int $teamId): Builder
    {
        return $this->join('statistics', 'statistics.fixture_id', '=', 'fixtures.id')
            ->join('markets', 'markets.id', '=', 'statistics.market_id')
            ->join('statistic_details as details1', 'details1.id', '=', 'statistics.statistic_details_1_id')
            ->leftJoin('statistic_details as details2', 'details2.id', '=', 'statistics.statistic_details_2_id')
            ->select([
                'fixtures.team_home',
                'fixtures.team_away',
                'markets.id as market_id',
                'markets.type as market_type',
                'statistics.score as statistic_score',
                'statistics.pretty_name as statistic_pretty_name',
                'statistics.type as statistic_type',
                'details1.value as statistic_1_value',
                'details1.count as statistic_1_count',
                'details1.config_pretty_name as statistic_1_config_pretty_name',
                'details1.config_type_id as statistic_1_config_type_id',
                'details1.config_window_size as statistic_1_config_window_size',
                'details1.config_threshold as statistic_1_config_threshold',
                'details1.config_threshold as statistic_1_config_threshold',
                'details2.value as statistic_2_value',
                'details2.count as statistic_2_count',
                'details2.config_pretty_name as statistic_2_config_pretty_name',
                'details2.config_type_id as statistic_2_config_type_id',
                'details2.config_window_size as statistic_2_config_window_size',
                'details2.config_threshold as statistic_2_config_threshold',
                'details2.config_threshold as statistic_2_config_threshold',
            ])
            ->where('details1.team_id', $teamId)
            ->whereIn('markets.type', [Betometer::ONE_X_TWO->value]);
    }

    public function formOfBothTeams(Fixture $fixture, int $limit, ?int $leagueId): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $date = date('Y-m-d', strtotime($fixture->date));

        $query = "
            SELECT * FROM (
                SELECT
                    f.winner,
                    f.date,
                    ? team_id,
                    IF(f.team_home = ?,1,0) playing_home,
                    f.team_home,
                    f.team_away
                FROM fixtures f
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    (f.team_home = ? OR f.team_away = ?) AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND
                    f.season_id = ls.season_id AND
                    DATE(f.date) < DATE(?)
                    {$leagueCondition}
                GROUP BY f.id
                ORDER BY f.date DESC
                LIMIT ?
            ) x
            UNION ALL
            SELECT * FROM (
                SELECT
                    f.winner,
                    f.date,
                    ? team_id,
                    IF(f.team_home = ?,1,0) playing_home,
                    f.team_home,
                    f.team_away
                FROM fixtures f
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE
                    (f.team_home = ? OR f.team_away = ?) AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND
                    f.season_id = ls.season_id AND
                    DATE(f.date) < DATE(?)
                    {$leagueCondition}
                GROUP BY f.id
                ORDER BY f.date DESC
                LIMIT ?
            ) x
        ";

        return collect(
            DB::select(
                $query,
                [
                    $fixture->team_home,
                    $fixture->team_home,
                    $fixture->team_home,
                    $fixture->team_home,
                    $date,
                    $limit,
                    $fixture->team_away,
                    $fixture->team_away,
                    $fixture->team_away,
                    $fixture->team_away,
                    $date,
                    $limit,
                ]
            )
        );
    }

    public function predictions(): HasMany
    {
        return $this->hasMany(FixturePrediction::class, 'fixture_id');
    }

    public function injuredPlayers(): HasMany
    {
        return $this->hasMany(PlayerInjury::class, 'fixture_id')
            ->join('players', 'players.id', '=', 'player_injuries.player_id');
    }

    public function getTodayFixturesHaventStartedYet(): Builder
    {
        return self::handleLeaguesRelation()
            ->whereRaw('DATE(fixtures.date) = CURDATE()')
            ->select(['fixtures.*']);
    }

    public function getLatestFixtureNonCurrentLeagueSeason(int $leagueId): Builder
    {
        return $this->join('league_seasons', function ($join) {
            $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
            $join->on('league_seasons.current', '=', DB::raw("0"));
        })
            ->where('fixtures.league_id', $leagueId)
            ->orderBy('league_seasons.season_id', 'desc');
    }

    public function fixturesInLeagueStreaks(int $leagueId): Builder
    {
        return $this->join('statistic_details as sd', 'sd.fixture_id', '=', 'fixtures.id')
            ->join('league_seasons as ls', 'ls.league_id', '=', 'fixtures.league_id')
            ->where('ls.league_id', $leagueId)
            ->where('ls.current', LeagueSeason::CURRENT_LEAGUE)
            ->whereDate('fixtures.date', '<', now())
            ->select('fixtures.*')
            ->distinct();
    }

    public function favorites(): MorphMany
    {
        return $this->morphMany(User::class, 'favoritable');
    }

    public function getNextMatchOrderByCountryAndLeague(Request $request, array $ids, string $date, string $tzOffset): Fixture|null
    {
        return $this
            ->handleLeaguesRelation()
            ->leftJoin('standings', function ($join) {
                $join->on('standings.league_id', '=', 'fixtures.league_id')
                    ->on('standings.season_id', '=', 'fixtures.season_id');
            })
            ->handleCountryLeagueRelation($request)
            ->handleRegionLeagueRelation($request)
            ->whereRaw("DATE(CONVERT_TZ(date, '+00:00', '$tzOffset')) = '$date'")
            ->whereNotIn('fixtures.status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->whereIn('fixtures.id', $ids)
            ->orderByRaw("
                CASE WHEN leagues.id IN (18, 180, 222, 360, 889) THEN 0 ELSE 1 END ASC,
                CASE WHEN countries_leagues_order._order IS NOT NULL THEN countries_leagues_order._order ELSE 99999 END ASC,
                CASE WHEN regions_leagues_order._order IS NOT NULL THEN regions_leagues_order._order ELSE 99999 END ASC,
                CASE WHEN standings.rank IS NOT NULL THEN standings.rank ELSE 99999 END ASC
            ")
            ->orderBy('has_predictions', 'DESC')
            ->orderBy('date')
            ->select(['fixtures.*'])
            ->first();
    }
}
