<?php

namespace App\Models\JogosHoje;

use \Illuminate\Database\Eloquent\Builder;
use App\Models\GuiidyModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\{Model, SoftDeletes};
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Watson\Validating\ValidatingTrait;

class FixtureEvent extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = 'fixture_events';

    protected $fillable = [
        'team_id',
        'fixture_id',
        'type',
        'detail',
        'time',
        'time_extra',
        'player_id',
        'assist_id',
        'player_name',
        'assist_name',
        'last_updated',
        '_id'
    ];

    protected ?array $rules = [
        'fixture_id' => 'required',
    ];

    protected $casts = [
        'time' => 'integer',
        'time_extra' => 'integer',
    ];

    protected $appends = [
        'detail_extra',
    ];

    public function fixture(): BelongsTo
    {
        return $this->belongsTo(Fixture::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function cardsForATeam(int $teamId, ?int $leagueId): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                f.id,
                IF(f.team_home = {$teamId}, 1, 0) played_home,
                COALESCE(ROUND((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'card' AND f.team_home = {$teamId},1,0))) / (IF((COUNT(DISTINCT fe.id) / COUNT(fe.id)) = 1, 1, 2))), 0) matrix_cards__team_home__1st_half,
                COALESCE(ROUND((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'card' AND f.team_home <> {$teamId},1,0))) / (IF((COUNT(DISTINCT fe.id) / COUNT(fe.id)) = 1, 1, 2))), 0) matrix_cards__team_away__1st_half,
                COALESCE(ROUND((SUM(IF(fe.team_id = {$teamId} AND (fe.time between 46 AND 90) AND fe.type = 'card' AND f.team_home = {$teamId},1,0))) / (IF((COUNT(DISTINCT fe.id) / COUNT(fe.id)) = 1, 1, 2))), 0) matrix_cards__team_home__2nd_half,
                COALESCE(ROUND((SUM(IF(fe.team_id = {$teamId} AND (fe.time between 46 AND 90) AND fe.type = 'card' AND f.team_home <> {$teamId},1,0))) / (IF((COUNT(DISTINCT fe.id) / COUNT(fe.id)) = 1, 1, 2))), 0) matrix_cards__team_away__2nd_half
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe on fe.fixture_id = f.id
            WHERE
                (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                f.season_id = ls.season_id AND
                f.status in ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL
                {$leagueCondition}
            GROUP BY
                f.id;
        ";

        return collect(DB::select($query));
    }

    public function goalsTimesForATeam(int $teamId, ?int $leagueId = null): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                f.id fixture_id,
                IF(f.team_home = {$teamId}, 1,0) played_home,
                IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), fe.time, NULL) matrix_time,
                IF(fe.team_id = {$teamId} AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 'scored', IF(fe.team_id <> {$teamId} AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 'conceded', NULL)) matrix_typeofgoal,
                fe.type
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe ON fe.fixture_id = f.id
            WHERE
                (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                f.season_id = ls.season_id AND
                f.status in ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL
                {$leagueCondition}
            ;
        ";

        return collect(DB::select($query));
    }

    public function goalsTotalsForATeam(int $teamId, ?int $leagueId = null): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                IF(f.team_home = {$teamId}, 1,0) played_home,
                {$teamId} team_id,
                IF(f.team_home = {$teamId}, f.goals_home + f.goals_away, 0) matrix_goals__team_home,
                IF(f.team_home <> {$teamId}, f.goals_home + f.goals_away, 0) matrix_goals__team_away
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            where
                (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                f.season_id = ls.season_id AND
                f.status in ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL
                {$leagueCondition}
            GROUP BY f.id;
        ";

        return collect(DB::select($query));
    }

    public function fulltimeFirstAndSecondHalfResultsForATeam(int $teamId, string $dateTime, ?int $leagueId = null, string $playingHome = "''"): QueryBuilder
    {
        $date = date('Y-m-d', strtotime($dateTime));

        return DB::table('fixtures')
            ->join('league_seasons', function ($join) {
                $join->on('league_seasons.league_id', '=', 'fixtures.league_id');
                $join->on('league_seasons.current', '=', DB::raw("1"));
            })
            ->join('teams', 'teams.id', '=', DB::raw($teamId))
            ->leftJoin('fixture_events', 'fixture_events.fixture_id', '=', 'fixtures.id')
            ->where(function ($q) use ($leagueId) {
                if ($leagueId) {
                    $q->where('fixtures.league_id', $leagueId);
                }
            })
            ->where(function ($q) use ($teamId) {
                $q->where('team_home', $teamId)
                    ->orWhere('team_away', $teamId);
            })
            ->whereNull('fixtures.deleted_at')
            ->whereRaw('fixtures.season_id = league_seasons.season_id')
            ->whereRaw("DATE(date) < '{$date}'")
            ->whereIn('status', ['FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO'])
            ->groupBy('fixtures.id')
            ->select([
                DB::raw("teams.name team_name"),
                DB::raw("{$playingHome} playing_home"),
                DB::raw("{$teamId} team_id"),
                DB::raw("IF(fixtures.team_home = {$teamId}, 1, 0) played_home"),
                DB::raw("(IF((fixtures.team_home = {$teamId} AND fixtures.winner = 'H') OR (fixtures.team_away = {$teamId} AND fixtures.winner = 'A'), 1, 0)) matrix_team_wins__fulltime"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND fixture_events.time <= 45, 1, 0)) > SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND fixture_events.time <= 45, 1, 0)), 1, 0)) matrix_team_wins__1st_half"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND (fixture_events.time BETWEEN 46 AND 90), 1, 0)) > SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND (fixture_events.time BETWEEN 46 AND 90), 1, 0)), 1, 0)) matrix_team_wins__2nd_half"),
                DB::raw("(IF(fixtures.goals_home = fixtures.goals_away, 1, 0)) matrix_team_draws__fulltime"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND fixture_events.time <= 45, 1, 0)) = SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND fixture_events.time <= 45, 1, 0)), 1, 0)) matrix_team_draws__1st_half"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND (fixture_events.time BETWEEN 46 AND 90), 1, 0)) = SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND (fixture_events.time BETWEEN 46 AND 90), 1, 0)), 1, 0)) matrix_team_draws__2nd_half"),
                DB::raw("(IF(NOT((fixtures.team_home = {$teamId} AND fixtures.winner = 'H') OR (fixtures.team_away = {$teamId} AND fixtures.winner = 'A')), 1, 0)) matrix_team_loss__fulltime"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND fixture_events.time <= 45, 1, 0)) < SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND fixture_events.time <= 45, 1, 0)), 1, 0)) matrix_team_loss__1st_half"),
                DB::raw("(IF(SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id = {$teamId} AND (fixture_events.time BETWEEN 46 AND 90), 1, 0)) < SUM(IF(fixture_events.type = 'goal' AND fixture_events.detail NOT IN ('missed penalty') AND fixture_events.team_id <> {$teamId} AND (fixture_events.time BETWEEN 46 AND 90),1,0)),1,0)) matrix_team_loss__2nd_half"),
            ]);
    }

    public function bothTeamsToScoreStatisticsForATeam(int $teamId, ?int $leagueId = null): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                IF(f.team_home = {$teamId}, 1, 0) played_home,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0), 1, 0) matrix_btts__fulltime,
                IF((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0), 1, 0) matrix_btts__1st_half,
                IF((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0), 1, 0) matrix_btts__2nd_half,
                IF(((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away, 0) > 0)) AND ((f.team_home = {$teamId} AND f.winner = 'H') OR (f.team_away = {$teamId} AND f.winner = 'A')), 1, 0) matrix_btts_win__fulltime,
                IF((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' and fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0))), 1, 0) matrix_btts_win__1st_half,
                IF((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' and fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0))), 1, 0) matrix_btts_win__2nd_half,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0) AND (f.winner IS NULL), 1, 0) matrix_btts_draw__fulltime,		
                IF((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) = SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0))), 1, 0) matrix_btts_draw__1st_half,	
                IF((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0)) = SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1, 0))), 1, 0) matrix_btts_draw__2nd_half,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0) AND (COALESCE(f.goals_home,0) + (COALESCE(f.goals_away,0)) > 2), 1, 0) matrix_btts_over25__fulltime,
                IF((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND ((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) + (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)))) > 2), 1, 0) matrix_btts_over25__1st_half,	
                IF((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND ((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) + (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)))) > 2), 1, 0) matrix_btts_over25__2nd_half,	
                IF(NOT((COALESCE(f.goals_home,0) > 0) AND (COALESCE(f.goals_away,0) > 0)) AND ((COALESCE(f.goals_home,0) + COALESCE(f.goals_away,0)) > 2), 1, 0) matrix_nobtts_over25__fulltime,	
                IF((NOT((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0))) AND ((SUM(IF(fe.team_id = {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) + (SUM(IF(fe.team_id <> {$teamId} AND fe.time <= 45 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)))) > 2), 1, 0) matrix_nobtts_over25__1st_half,                
                IF((NOT((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0) AND (SUM(IF(fe.team_id <> {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) > 0))) AND ((SUM(IF(fe.team_id = {$teamId} AND (fe.time BETWEEN 46 AND 90) AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)) + (SUM(IF(fe.team_id <> {$teamId} AND fe.time BETWEEN 46 AND 90 AND fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'), 1,0)))) > 2), 1, 0) matrix_nobtts_over25__2nd_half                
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe ON fe.fixture_id = f.id
            WHERE
                (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                f.season_id = ls.season_id AND
                f.status in ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL
                {$leagueCondition}
            GROUP BY f.id;            
        ";

        return collect(DB::select($query));
    }

    public function cornersForATeam(int $teamId, ?int $leagueId = null): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                IF(f.team_home = {$teamId}, 1, 0) played_home,
                IF(f.team_home = {$teamId}, f.goals_home + f.goals_away, 0) matrix_corners__team_home,
                IF(f.team_home <> {$teamId}, f.goals_home + f.goals_away, 0) matrix_corners__team_away
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                (f.team_home = {$teamId} OR f.team_away = {$teamId}) AND
                f.season_id = ls.season_id AND
                f.status in ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL
                {$leagueCondition}
            GROUP BY
                f.id;
        ";

        return collect(DB::select($query));
    }

    public function goalsForTeams(int $homeTeam, int $awayTeam, string $type, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        switch ($type) {
            case 'in_favor': {
                $homeCondition = "AND fe.team_id = " . (string) $homeTeam;
                $awayCondition = "AND fe.team_id = " . (string) $awayTeam;

                break;
            }
            case 'against': {
                $homeCondition = "AND fe.team_id <> " . (string) $homeTeam;
                $awayCondition = "AND fe.team_id <> " . (string) $awayTeam;
                break;
            }
            default:
                $homeCondition = '';
                $awayCondition = '';
                break;
        }

        $query = "
            SELECT
                ? team_id,
                IF(f.team_home = ?, 1, 0) played_home,
                SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND time <= 45 {$homeCondition},1,0)) matrix_goals__team_home__1st_half,
                SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND time > 45 {$homeCondition},1,0)) matrix_goals__team_home__2nd_half,
                0 matrix_goals__team_away__1st_half,
                0 matrix_goals__team_away__2nd_half
            FROM fixtures f 
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1 
            LEFT JOIN fixture_events fe ON f.id = fe.fixture_id 
            WHERE
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND                
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND                 
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
            UNION ALL
            SELECT
                ? team_id,
                IF(f.team_home = ?, 1, 0) played_home,
                0 matrix_goals__team_home__1st_half,
                0 matrix_goals__team_home__2nd_half,
                SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND time <= 45 {$awayCondition},1,0)) matrix_goals__team_away__1st_half,
                SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND time > 45 {$awayCondition},1,0)) matrix_goals__team_away__2nd_half
            FROM fixtures f 
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1 
            LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
            WHERE
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND                
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND 
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function bothTeamsToScoreForTeams(int $homeTeam, int $awayTeam, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                ? team_id,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0), 1, 0) matrix_btts__team_home__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1, 0)) > 0), 1, 0) matrix_btts__team_home__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0)) > 0), 1, 0) matrix_btts__team_home__2nd_half,
                IF(((COALESCE(f.goals_home,0) > 0) AND (COALESCE(f.goals_away,0) > 0)) AND ((f.team_home = ? AND f.winner = 'H') OR (f.team_away = ? AND f.winner = 'A')), 1, 0) matrix_btts_win__team_home__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0))), 1, 0) matrix_btts_win__team_home__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0))), 1, 0) matrix_btts_win__team_home__2nd_half,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0) AND (f.winner IS NULL), 1, 0) matrix_btts_draw__team_home__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) = SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1, 0))), 1, 0) matrix_btts_draw__team_home__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) = SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0))), 1, 0) matrix_btts_draw__team_home__2nd_half,
                IF(((COALESCE(goals_home,0) > 0) AND (COALESCE(goals_away,0) > 0)) AND ((COALESCE(goals_home,0) + COALESCE(goals_away,0)) > 2), 1, 0) matrix_btts_over25__team_home__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)))) > 2), 1, 0) matrix_btts_over25__team_home__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)))) > 2), 1, 0) matrix_btts_over25__team_home__2nd_half,
                IF((NOT((COALESCE(goals_home,0) > 0) AND (COALESCE(goals_away,0) > 0))) AND ((COALESCE(goals_home,0) + COALESCE(goals_away,0)) > 2), 1, 0) matrix_nobtts_over25__team_home__fulltime,
                IF((NOT((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0))) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)))) > 2), 1, 0) matrix_nobtts_over25__team_home__1st_half,
                IF((NOT((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0))) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)))) > 2), 1, 0) matrix_nobtts_over25__team_home__2nd_half,
                0 matrix_btts__team_away__fulltime,
                0 matrix_btts__team_away__1st_half,
                0 matrix_btts__team_away__2nd_half,
                0 matrix_btts_win__team_away__fulltime,
                0 matrix_btts_win__team_away__1st_half,
                0 matrix_btts_win__team_away__2nd_half,
                0 matrix_btts_draw__team_away__fulltime,
                0 matrix_btts_draw__team_away__1st_half,
                0 matrix_btts_draw__team_away__2nd_half,
                0 matrix_btts_over25__team_away__fulltime,
                0 matrix_btts_over25__team_away__1st_half,
                0 matrix_btts_over25__team_away__2nd_half,
                0 matrix_nobtts_over25__team_away__fulltime,
                0 matrix_nobtts_over25__team_away__1st_half,
                0 matrix_nobtts_over25__team_away__2nd_half
            FROM fixtures f                        
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
            WHERE 
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND 
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
            UNION ALL
            SELECT
                ? team_id,
                0 matrix_btts__team_home__fulltime,
                0 matrix_btts__team_home__1st_half,
                0 matrix_btts__team_home__2nd_half,
                0 matrix_btts_win__team_home__fulltime,
                0 matrix_btts_win__team_home__1st_half,
                0 matrix_btts_win__team_home__2nd_half,
                0 matrix_btts_draw__team_home__fulltime,
                0 matrix_btts_draw__team_home__1st_half,
                0 matrix_btts_draw__team_home__2nd_half,
                0 matrix_btts_over25__team_home__fulltime,
                0 matrix_btts_over25__team_home__1st_half,
                0 matrix_btts_over25__team_home__2nd_half,
                0 matrix_nobtts_over25__team_home__fulltime,
                0 matrix_nobtts_over25__team_home__1st_half,
                0 matrix_nobtts_over25__team_home__2nd_half,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0), 1, 0) matrix_btts__team_away__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1, 0)) > 0), 1, 0) matrix_btts__team_away__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0)) > 0), 1, 0) matrix_btts__team_away__2nd_half,
                IF(((COALESCE(f.goals_home,0) > 0) AND (COALESCE(f.goals_away,0) > 0)) AND ((f.team_home = ? AND f.winner = 'H') OR (f.team_away = ? AND f.winner = 'A')), 1, 0) matrix_btts_win__team_away__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0))), 1, 0) matrix_btts_win__team_away__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1, 0))), 1, 0) matrix_btts_win__team_away__2nd_half,
                IF((COALESCE(f.goals_home, 0) > 0) AND (COALESCE(f.goals_away,0) > 0) AND (f.winner IS NULL), 1, 0) matrix_btts_draw__team_away__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1, 0)) = SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1, 0))), 1, 0) matrix_btts_draw__team_away__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) = SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0))), 1, 0) matrix_btts_draw__team_away__2nd_half,
                IF(((COALESCE(goals_home,0) > 0) AND (COALESCE(goals_away,0) > 0)) AND ((COALESCE(goals_home,0) + COALESCE(goals_away,0)) > 2), 1, 0) matrix_btts_over25__team_away__fulltime,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)))) > 2), 1, 0) matrix_btts_over25__team_away__1st_half,
                IF((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)))) > 2), 1, 0) matrix_btts_over25__team_away__2nd_half,
                IF((NOT((COALESCE(goals_home,0) > 0) AND (COALESCE(goals_away,0) > 0))) AND ((COALESCE(goals_home,0) + COALESCE(goals_away,0)) > 2), 1, 0) matrix_nobtts_over25__team_away__fulltime,
                IF((NOT((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)) > 0))) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time <= 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time <= 45, 1,0)))) > 2), 1, 0) matrix_nobtts_over25__team_away__1st_half,
                IF((NOT((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) > 0) AND (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)) > 0))) AND ((SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id = ? AND fe.time > 45, 1,0)) + (SUM(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty') AND fe.team_id <> ? AND fe.time > 45, 1,0)))) > 2), 1, 0) matrix_nobtts_over25__team_away__2nd_half
            FROM fixtures f            
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe on f.id = fe.fixture_id
            WHERE 
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND                 
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
        ";

        $data = array_fill(0, 39, $homeTeam);
        $data = array_merge($data, [$homeAway, $homeTeam, $homeTeam, $homeAway, $homeTeam, $dateTime]);
        $data = array_merge($data, array_fill(0, 39, $awayTeam));
        $data = array_merge($data, [$homeAway, $awayTeam, $awayTeam, $homeAway, $awayTeam, $dateTime]);

        return collect(
            DB::select(
                $query,
                $data
            )
        );
    }

    public function topScorers(int $homeTeam, int $awayTeam, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                * 
            FROM 
                (SELECT                    
                    ? team_id,
                    p.name,
                    p.api_id,
                    p.firstname,
                    p.lastname,
                    p.number,
                    p.height,
                    p.weight,
                    p.injured,
                    p.birth_date,
                    p.birth_place,
                    p.birth_country,
                    p.media_site_id,
                    p.id,
                    COUNT(*) value 
                FROM fixture_events fe
                JOIN fixtures f ON f.id = fe.fixture_id
                JOIN players p on p.id = fe.player_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE 
                    fe.type = 'goal' AND
                    fe.detail NOT IN ('missed penalty') AND
                    fe.team_id = ? AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND 
                    f.deleted_at IS NULL AND 
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND 
                    ls.season_id = f.season_id AND
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY p.id
                ORDER BY value DESC
                LIMIT 6
            ) x
            UNION ALL
            SELECT 
                * 
            FROM 
                (SELECT                    
                    ? team_id,                    
                    p.name,
                    p.api_id,
                    p.firstname,
                    p.lastname,
                    p.number,
                    p.height,
                    p.weight,
                    p.injured,
                    p.birth_date,
                    p.birth_place,
                    p.birth_country,
                    p.media_site_id,
                    p.id,
                    COUNT(*) value 
                FROM fixture_events fe
                JOIN fixtures f ON f.id = fe.fixture_id
                JOIN players p on p.id = fe.player_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE                     
                    fe.type = 'goal' AND
                    fe.detail NOT IN ('missed penalty') AND
                    fe.team_id = ? AND
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND 
                    f.deleted_at IS NULL AND 
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                    ls.season_id = f.season_id AND
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY p.id
                ORDER BY value DESC
                LIMIT 6
            ) x            
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function whoWillScoreFirst(int $homeTeam, int $awayTeam, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                GROUP_CONCAT(DISTINCT x.team_id) team_id, 
                GROUP_CONCAT(DISTINCT team_name) team_name, 
                COALESCE(SUM(x.first_team_to_score),0) num,
                10 max
            FROM
                (SELECT 
                    ? team_id,
                    t.name team_name,
                    IF((GROUP_CONCAT(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'),fe.team_id,NULL) ORDER BY fe.time ASC) - 0) = ?, 1,0) first_team_to_score
                FROM fixtures f 
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                JOIN teams t ON t.id = ?
                LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
                WHERE 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND
                    ls.season_id = f.season_id AND
                    f.deleted_at IS NULL AND 
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY f.id
                ORDER BY f.date DESC
                LIMIT 10
            ) x
            UNION ALL
            SELECT 
                GROUP_CONCAT(DISTINCT x.team_id) team_id, 
                GROUP_CONCAT(DISTINCT team_name) team_name, 
                COALESCE(SUM(x.first_team_to_score),0) num,
                10 max
            FROM
                (SELECT 
                    ? team_id,
                    t.name team_name,
                    IF((GROUP_CONCAT(IF(fe.type = 'goal' AND fe.detail NOT IN ('missed penalty'),fe.team_id,NULL) ORDER BY fe.time ASC) - 0) = ?, 1,0) first_team_to_score
                FROM fixtures f
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                JOIN teams t ON t.id = ?
                LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
                WHERE 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                    ls.season_id = f.season_id AND 
                    f.deleted_at IS NULL AND 
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY f.id
                ORDER BY f.date DESC
                limit 10
            ) x
        ";

        $data = array_fill(0, 3, $homeTeam);
        $data = array_merge($data, [$homeAway, $homeTeam, $homeTeam, $homeAway, $homeTeam, $dateTime]);
        $data = array_merge($data, array_fill(0, 3, $awayTeam));
        $data = array_merge($data, [$homeAway, $awayTeam, $awayTeam, $homeAway, $awayTeam, $dateTime]);

        return collect(
            DB::select(
                $query,
                $data
            )
        );
    }

    public function goalsTimesForTeams(int $homeTeam, int $awayTeam, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                {$homeTeam} team_id,
                COUNT(*) games,
                COALESCE(SUM(scored__0_15_min), 0) scored__0_15_min,
                COALESCE(SUM(scored__15_30_min), 0) scored__15_30_min,
                COALESCE(SUM(scored__30_45_min), 0) scored__30_45_min,
                COALESCE(SUM(scored__45_60_min), 0) scored__45_60_min,
                COALESCE(SUM(scored__60_75_min), 0) scored__60_75_min,
                COALESCE(SUM(scored__75_90_min), 0) scored__75_90_min,
                COALESCE(SUM(conceded__0_15_min), 0) conceded__0_15_min,
                COALESCE(SUM(conceded__15_30_min), 0) conceded__15_30_min,
                COALESCE(SUM(conceded__30_45_min), 0) conceded__30_45_min,
                COALESCE(SUM(conceded__45_60_min), 0) conceded__45_60_min,
                COALESCE(SUM(conceded__60_75_min), 0) conceded__60_75_min,
                COALESCE(SUM(conceded__75_90_min), 0) conceded__75_90_min
            FROM (
                SELECT
                    f.id,
                    f.season_id,
                    f.date,
                    f.status,
                    t.id team_id,
                    t.name team_name,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 0 AND 15, 1, 0)) scored__0_15_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 16 AND 30, 1, 0)) scored__15_30_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 31 AND 45, 1, 0)) scored__30_45_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 46 AND 60, 1, 0)) scored__45_60_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 61 AND 75, 1, 0)) scored__60_75_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$homeTeam} AND fe.time BETWEEN 76 AND 90, 1, 0)) scored__75_90_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 0 AND 15, 1, 0)) conceded__0_15_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 16 AND 30, 1, 0)) conceded__15_30_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 31 AND 45, 1, 0)) conceded__30_45_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 46 AND 60, 1, 0)) conceded__45_60_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 61 AND 75, 1, 0)) conceded__60_75_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$homeTeam} AND fe.time BETWEEN 76 AND 90, 1, 0)) conceded__75_90_min
                FROM fixtures f 
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                JOIN teams t ON t.id = f.team_home
                LEFT JOIN fixture_events fe ON fe.fixture_id = f.id
                WHERE 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    (({$homeAway} <> 1 AND (f.team_home = {$homeTeam} OR f.team_away = {$homeTeam})) OR (({$homeAway} = 1) AND (f.team_home = {$homeTeam}))) AND
                    f.season_id = ls.season_id AND 
                    f.deleted_at IS NULL AND 
                    fe.type = 'goal' AND 
                    fe.detail <> 'missed penalty' AND 
                    DATE(f.date) < DATE(?)
                    {$leagueCondition}
                    GROUP BY f.id
                    ORDER BY f.date
                ) y
            UNION ALL
            SELECT 
                {$awayTeam} team_id,
                COUNT(*) games,
                COALESCE(SUM(scored__0_15_min), 0) scored__0_15_min,
                COALESCE(SUM(scored__15_30_min), 0) scored__15_30_min,
                COALESCE(SUM(scored__30_45_min), 0) scored__30_45_min,
                COALESCE(SUM(scored__45_60_min), 0) scored__45_60_min,
                COALESCE(SUM(scored__60_75_min), 0) scored__60_75_min,
                COALESCE(SUM(scored__75_90_min), 0) scored__75_90_min,
                COALESCE(SUM(conceded__0_15_min), 0) conceded__0_15_min,
                COALESCE(SUM(conceded__15_30_min), 0) conceded__15_30_min,
                COALESCE(SUM(conceded__30_45_min), 0) conceded__30_45_min,
                COALESCE(SUM(conceded__45_60_min), 0) conceded__45_60_min,
                COALESCE(SUM(conceded__60_75_min), 0) conceded__60_75_min,
                COALESCE(SUM(conceded__75_90_min), 0) conceded__75_90_min
            FROM (
                SELECT
                    f.id,
                    f.season_id,
                    f.date,
                    f.status,
                    t.id team_id,
                    t.name team_name,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 0 AND 15, 1, 0)) scored__0_15_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 16 AND 30, 1, 0)) scored__15_30_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 31 AND 45, 1, 0)) scored__30_45_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 46 AND 60, 1, 0)) scored__45_60_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 61 AND 75, 1, 0)) scored__60_75_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id = {$awayTeam} AND fe.time BETWEEN 76 AND 90, 1, 0)) scored__75_90_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 0 AND 15, 1, 0)) conceded__0_15_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 16 AND 30, 1, 0)) conceded__15_30_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 31 AND 45, 1, 0)) conceded__30_45_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 46 AND 60, 1, 0)) conceded__45_60_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 61 AND 75, 1, 0)) conceded__60_75_min,
                    SUM(IF(fe.fixture_id = f.id AND fe.team_id <> {$awayTeam} AND fe.time BETWEEN 76 AND 90, 1, 0)) conceded__75_90_min
                FROM fixtures f 
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                JOIN teams t ON t.id = f.team_home
                LEFT JOIN fixture_events fe ON fe.fixture_id = f.id
                WHERE 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    (({$homeAway} <> 1 AND (f.team_home = {$awayTeam} OR f.team_away = {$awayTeam})) OR (({$homeAway} = 1) AND (f.team_away = {$awayTeam}))) AND
                    f.season_id = ls.season_id AND 
                    f.deleted_at IS NULL AND 
                    fe.type = 'goal' AND 
                    fe.detail <> 'missed penalty' AND 
                    DATE(f.date) < DATE(?)
                    {$leagueCondition}
                    GROUP BY f.id
                    ORDER BY f.date
                ) y
        ";

        return collect(DB::select($query, [$dateTime, $dateTime]));
    }

    public function avgCardsOrCornersForTeams(int $homeTeam, int $awayTeam, string $eventType, string $type, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        switch ($type) {
            case 'in_favor': {
                $homeCondition = "IF(f.team_home = {$homeTeam}, f.{$eventType}s_home, f.{$eventType}s_away)";
                $awayCondition = "IF(f.team_away = {$awayTeam}, f.{$eventType}s_away, f.{$eventType}s_home)";

                break;
            }
            case 'against': {
                $homeCondition = "IF(f.team_home <> {$homeTeam}, f.{$eventType}s_home, f.{$eventType}s_away)";
                $awayCondition = "IF(f.team_away <> {$awayTeam}, f.{$eventType}s_away, f.{$eventType}s_home)";

                break;
            }
            default: {
                $homeCondition = "f.{$eventType}s_home + f.{$eventType}s_away";
                $awayCondition = "f.{$eventType}s_home + f.{$eventType}s_away";

                break;
            }
        }

        $query = "
            SELECT
                t.name,
                COUNT(*) total_games,
                COALESCE(SUM({$homeCondition}), 0) total_{$eventType}s,
                COALESCE((SUM({$homeCondition}) / COUNT(*)), 0) avg
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            JOIN teams t ON t.id = ?
            WHERE
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                $leagueCondition
            UNION ALL
            SELECT
                t.name,
                COUNT(*) total_games,
                COALESCE(SUM({$awayCondition}), 0) total_{$eventType}s,
                COALESCE((SUM({$awayCondition}) / COUNT(*)), 0) avg
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            JOIN teams t ON t.id = ?
            WHERE
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                f.season_id = ls.season_id AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                $leagueCondition
            ;
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function maxCardsOrCornersForTeams(string $eventType, string $dateTime, ?int $leagueId = null): ?float
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                MAX(f.{$eventType}s_home + f.{$eventType}s_away) max
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL AND 
                f.season_id = ls.season_id AND
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
        ";

        return DB::selectOne($query, [$dateTime])->max ?? null;
    }

    public function cardsOrCornersForTeams(int $homeTeam, int $awayTeam, string $eventType, string $type, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        switch ($type) {
            case 'in_favor': {
                $homeCondition = "AND fe.team_id <> " . (string) $homeTeam;
                $awayCondition = "AND fe.team_id <> " . (string) $awayTeam;

                break;
            }
            case 'against': {
                $homeCondition = "AND fe.team_id = " . (string) $homeTeam;
                $awayCondition = "AND fe.team_id = " . (string) $awayTeam;

                break;
            }
            default: {
                $homeCondition = "";
                $awayCondition = "";

                break;
            }
        }

        $query = "
            SELECT 
                ? team_id,
                (SUM(IF(fe.type = '{$eventType}' AND fe.time <= 45 {$homeCondition},1,0))) matrix_{$eventType}s__team_home__1st_half,
                (SUM(IF(fe.type = '{$eventType}' AND fe.time > 45 {$homeCondition},1,0))) matrix_{$eventType}s__team_home__2nd_half,
                0 matrix_{$eventType}s__team_away__1st_half,
                0 matrix_{$eventType}s__team_away__2nd_half
            FROM fixtures f 
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1 
            LEFT JOIN fixture_events fe ON f.id = fe.fixture_id
            WHERE                
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND
                f.season_id = ls.season_id AND 
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
            UNION ALL
            SELECT 
                ? team_id,
                0 matrix_{$eventType}s__team_home__1st_half,
                0 matrix_{$eventType}s__team_home__2nd_half,
                (SUM(IF(fe.type = '{$eventType}' AND fe.time <= 45 {$awayCondition},1,0))) matrix_{$eventType}s__team_away__1st_half,
                (SUM(IF(fe.type = '{$eventType}' AND fe.time > 45 {$awayCondition},1,0))) matrix_{$eventType}s__team_away__2nd_half
            FROM fixtures f                        
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            LEFT JOIN fixture_events fe on f.id = fe.fixture_id
            WHERE                
                ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                f.season_id = ls.season_id AND 
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.deleted_at IS NULL AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            GROUP BY f.id
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function topCards(int $homeTeam, int $awayTeam, string $dateTime, ?int $leagueId = null, ?int $homeAway = 0): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                * 
            FROM 
                (SELECT
                    ? team_id,
                    p.name,
                    p.api_id,
                    p.firstname,
                    p.lastname,
                    p.number,
                    p.height,
                    p.weight,
                    p.injured,
                    p.birth_date,
                    p.birth_place,
                    p.birth_country,
                    p.media_site_id,
                    p.id,
                    COUNT(*) value 
                FROM fixture_events fe
                JOIN fixtures f ON f.id = fe.fixture_id
                JOIN players p on p.id = fe.player_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE 
                    fe.type = 'card' AND
                    fe.team_id = ? AND
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_home = ?))) AND 
                    ls.season_id = f.season_id AND 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND 
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY p.id
                ORDER BY value DESC
                LIMIT 6
            ) x
            UNION ALL
            SELECT 
                * 
            FROM 
                (SELECT
                    ? team_id,
                    p.name,
                    p.api_id,
                    p.firstname,
                    p.lastname,
                    p.number,
                    p.height,
                    p.weight,
                    p.injured,
                    p.birth_date,
                    p.birth_place,
                    p.birth_country,
                    p.media_site_id,
                    p.id,
                    COUNT(*) value 
                FROM fixture_events fe
                JOIN fixtures f ON f.id = fe.fixture_id
                JOIN players p on p.id = fe.player_id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                WHERE                     
                    fe.type = 'card' AND
                    fe.team_id = ? AND
                    ((? <> 1 AND (f.team_home = ? OR f.team_away = ?)) OR ((? = 1) AND (f.team_away = ?))) AND
                    ls.season_id = f.season_id AND 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.deleted_at IS NULL AND 
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY p.id
                ORDER BY value DESC
                LIMIT 6
            ) x            
        ";

        return collect(
            DB::select(
                $query,
                [
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $homeTeam,
                    $homeAway,
                    $homeTeam,
                    $dateTime,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $awayTeam,
                    $homeAway,
                    $awayTeam,
                    $dateTime,
                ]
            )
        );
    }

    public function avgRefereeCardsStatistics(int $refereeId, int $leagueId, string $dateTime): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT 
                SUM(total_yellow_cards) total_yellow_cards,
                SUM(total_red_cards) total_red_cards,
                SUM(games) total_games,
                COALESCE(SUM(total_yellow_cards) / SUM(games), 0) avg_yellow_cards,
                COALESCE(SUM(total_red_cards) / SUM(games), 0) avg_red_cards
            FROM (
                SELECT 
                    f.id,
                    (SUM(IF(fe.type = 'card' AND fe.detail = 'yellow card',1,0)) / 2) total_yellow_cards,
                    (SUM(IF(fe.type = 'card' AND fe.detail = 'red card',1,0)) / 2) total_red_cards,
                    1 games
                FROM fixtures f 
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                LEFT JOIN fixture_events fe on fe.fixture_id = f.id
                WHERE 
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.referee_id IS NOT NULL AND
                    f.season_id = ls.season_id AND
                    f.deleted_at IS NULL AND 
                    f.referee_id = ? AND 
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                group by f.id
            ) x;
        ";

        return collect(
            DB::selectOne(
                $query,
                [
                    $refereeId,
                    $dateTime,
                ]
            )
        );
    }

    public function refereeCardsStatistics(int $refereeId, int $leagueId, string $dateTime): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                (cards_home + cards_away) cards
            FROM fixtures f
            JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
            WHERE
                f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                f.referee_id IS NOT NULL AND 
                f.season_id = ls.season_id AND 
                f.deleted_at IS NULL AND 
                f.referee_id = ? AND 
                DATE(f.date) < DATE(?) 
                {$leagueCondition}
            ;
        ";

        return collect(
            DB::select(
                $query,
                [
                    $refereeId,
                    $dateTime,
                ]
            )
        );
    }

    public function cornersAndCardsStatsForALeague(int $leagueId, string $type, int $threshold, string $operator, ?int $seasonId = null): Collection
    {
        $query = "
            SELECT 
                w.team_id,
                media_site_id, 
                t.name, 
                SUM(amount) {$type}s, SUM(games) games, ROUND(((SUM(amount) / SUM(games)) * 100),2) percent, t.media_site_id FROM (
                    SELECT 
                        *, 
                        IF(type {$operator} ?, 1, 0) amount FROM (
                            SELECT 
                                fe.team_id team_id,
                                GROUP_CONCAT(DISTINCT x.type) type,
                                COUNT(DISTINCT f.id) games, 
                                f.id fixture_id 
                            FROM  fixture_events fe 
                            JOIN (
                                SELECT 
                                    f.id,
                                    GROUP_CONCAT(DISTINCT fe.team_id) - 0 team_id,
                                    SUM(1) type
                                FROM fixture_events fe
                                JOIN fixtures f ON f.id = fe.fixture_id 
                                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                                WHERE
                                    fe.type = '{$type}' AND
                                    f.league_id = ? AND
                                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                                    f.deleted_at IS NULL AND 
                                    f.season_id = " . ($seasonId ?? 'ls.season_id') . "
                                GROUP BY f.id
                            ) x ON x.id = fe.fixture_id
                        JOIN fixtures f ON f.id = fe.fixture_id
                        JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                        WHERE
                            fe.type = '{$type}' AND
                            f.league_id = ? AND
                            f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                            f.deleted_at IS NULL AND 
                            f.season_id = " . ($seasonId ?? 'ls.season_id') . "
                        GROUP BY fe.team_id, f.id
                    ) y
                ) w 
            JOIN teams t ON t.id = w.team_id
            GROUP BY w.team_id 
            ORDER BY sum(amount) DESC;
        ";

        return collect(
            DB::select(
                $query,
                [
                    $threshold,
                    $leagueId,
                    $leagueId
                ]
            )
        );
    }

    public function getDetailExtraAttribute()
    {
        return null;
    }
}