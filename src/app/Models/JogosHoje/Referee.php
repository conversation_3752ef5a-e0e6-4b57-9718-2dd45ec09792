<?php

namespace App\Models\JogosHoje;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\{Model, SoftDeletes};
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Watson\Validating\ValidatingTrait;

class Referee extends Model
{
    use HasFactory;
    use ValidatingTrait;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'normalized_name',
        'country_code',
    ];

    protected ?array $rules = [
        'name' => 'required',
    ];

    public function fixtures(): HasMany
    {
        return $this->hasMany(Fixture::class);
    }

    public function cardsInfo(Fixture $fixture, int $leagueId, string $dateTime): Collection
    {
        $leagueCondition = $leagueId ? " AND f.league_id = {$leagueId}" : "";

        $query = "
            SELECT
                COALESCE((SUM(yellow_cards) / COUNT(*)), 0) yellow_cards,
                COALESCE((SUM(red_cards) / COUNT(*)), 0) red_cards,
                COUNT(*) games
            FROM (
                SELECT 
                    1 games,
                    (SUM(IF(fe.detail = 'yellow card' AND fe.type = 'card',1,0)) / 2) yellow_cards, 
                    (SUM(IF(fe.detail = 'red card' AND fe.type = 'card',1,0)) / 2) red_cards 	
                FROM referees r
                JOIN fixtures f ON f.referee_id = r.id
                JOIN league_seasons ls ON ls.league_id = f.league_id AND ls.current = 1
                LEFT JOIN fixture_events fe ON fe.fixture_id = f.id
                WHERE
                    f.status IN ('FT', 'AET', 'PEN', 'ABD', 'CANC', 'AWD', 'WO') AND
                    f.referee_id IS NOT NULL AND
                    f.season_id = ls.season_id AND
                    f.deleted_at IS NULL AND 
                    r.id = ? AND
                    DATE(f.date) < DATE(?) 
                    {$leagueCondition}
                GROUP BY f.id
            ) t;
        ";

        return collect(
            DB::select(
                $query,
                [
                    $fixture->referee_id,
                    $dateTime,
                ]
            )
        );
    }
}
