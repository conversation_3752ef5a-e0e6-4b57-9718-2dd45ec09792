import {useEffect, useRef} from 'react';
import Checkbox from '@/Components/Checkbox';
import GuestLayout from '@/Layouts/GuestLayout';
import InputError from '@/Components/InputError';
import InputLabel from '@/Components/InputLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import {Head, Link, useForm} from '@inertiajs/react';
import ReCAPTCHA from "react-google-recaptcha";

export default function Login({status, canResetPassword, recaptcha}) {
    const recaptchaRef = useRef();
    const { data, setData, post, processing, errors, reset } = useForm({
        email: '',
        password: '',
        remember: '',
        'g-recaptcha-response': ''
    });

    useEffect(() => {
        return () => {
            reset('password');
        };
    }, []);

    const onHandleChange = (event) => {
        setData(event.target.name, event.target.type === 'checkbox' ? event.target.checked : event.target.value);
    };

    const submit = (e) => {
        e.preventDefault();
        post(route('login'));
    };

    const onChange = (value) => {
        setData('g-recaptcha-response', value)
    }

    return (
        <>
            <GuestLayout>
                <Head title="Log in"/>

                <form onSubmit={submit}>
                    <div>
                        <InputLabel forInput="email" value="Email" />

                        <TextInput
                            id="email"
                            type="email"
                            name="email"
                            value={data.email}
                            className="mt-1 block w-full rounded border-gray-300"
                            autoComplete="username"
                            isFocused={true}
                            placeholder="<EMAIL>"
                            handleChange={onHandleChange}
                        />

                        <InputError message={errors.email} className="mt-2" />
                    </div>

                    <div className="mt-4">
                        <InputLabel forInput="password" value="Password" />

                        <TextInput
                            id="password"
                            type="password"
                            name="password"
                            value={data.password}
                            className="mt-1 block w-full rounded border-gray-300"
                            autoComplete="current-password"
                            placeholder="Type your password"
                            handleChange={onHandleChange}
                        />

                        <InputError message={errors.password} className="mt-2" />
                    </div>

                    {/*
                    <div className="block mt-4">
                        <label className="flex items-center">
                            <Checkbox name="remember" value={data.remember} handleChange={onHandleChange} />
                            <span className="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                    </div>
                    */}

                    {process.env.REACT_APP_APP_ENV !== 'local' && (
                        <div className="block mt-4">
                            <ReCAPTCHA
                                ref={recaptchaRef}
                                sitekey={recaptcha.site_key}
                                onChange={onChange}
                            />
                            <TextInput
                                id="captcha"
                                type="hidden"
                                name="g-recaptcha-response"
                                value={data['g-recaptcha-response']}
                                className="mt-1 block w-full rounded border-gray-300"
                            />
                            {status && <div className="mb-4 font-medium text-sm text-red-600">{status}</div>}
                        </div>)}

                    <div className="flex items-center justify-between mt-4">
                        <div>
                            <Link
                                href={route('register')}
                                className="text-sm text-gray-700 underline"
                            >
                                Create account?
                            </Link>
                        </div>
                        <div>
                            {canResetPassword && (
                                <Link
                                    href={route('password.request')}
                                    className="underline underline-offset-2 text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Forgot your password?
                                </Link>
                            )}

                            <PrimaryButton className="ml-4" processing={processing}>
                                Log in
                            </PrimaryButton>
                        </div>
                    </div>
                </form>
            </GuestLayout>
        </>
    );
}
