import { io } from 'socket.io-client';

export const socket = ['local', 'staging', 'development', 'qa'].indexOf(window.process.env.REACT_APP_APP_ENV) > -1 ? false : io('https://sockets-cms.jogoshoje.com');
//export const socket = io('http://localhost:6001')


// Socket server URL. In production should be: https://sockets.jogoshoje.com 
/*
* Socket node server in production uses PM2 to handle the service | https://pm2.keymetrics.io/docs/usage/quick-start/
*
* $ pm2 start app.js --name my-api      # Name process
* $ pm2 list                            # Display all processes status
* $ pm2 stop all                        # Stop all processes
* $ pm2 restart all                     # Restart all processes
* $ pm2 delete all                      # Will remove process from pm2 list
*
* More details here: https://pm2.keymetrics.io/docs/usage/pm2-doc-single-page/
*/