require('dotenv').config()

var express  = require('express'),
    _ = require('lodash'),
    app = express(),
    server = require("http").createServer(app),
    options = {
    	cors: {
		    origin: ['http://localhost:888', 'https://cms.altsdigital.com', 'https://staging-cms.altsdigital.com', 'https://dev-cms.altsdigital.com', 'https://qa-cms.altsdigital.com', 'https://cms.jogoshoje.com'],
		    methods: ['GET', 'POST']
		}
    },
    io = require("socket.io")(server, options)
    port = 6001;

var usersSocketsIds = {}; // (user)id => [socketIds1, socketIds2, socketIds3]
var users = {}; // map: (user)id => name
var usersOnARoom = {}
var usersJoinOnARoom = {};

function getUsersOnContent(socketsInRoom, room) {
    var arr = [];
    for(var socketId in socketsInRoom) {
        if (!_.filter(arr, function(a) { return a.id == socketsInRoom[socketId]; }).length) {
            arr.push(users[socketsInRoom[socketId]]);
        }
    }

    return arr.map((user, k) => { return Object.assign({}, user, { joined_at: usersJoinOnARoom[room][user.id] } ) });
}

server.listen(port, function(){
    console.log('listening on *:' + port);
});

io.on('connection', function(socket) {
    socket.on('HELLO', function(data) {
        var user = data.user;
        var room = data.pathname;

        // A map with User ID => User object (if no exists)
        if (!users[user.id]) {
            users[user.id] = Object.assign({}, user, { joined_at: Date.now() });
        }

        // Save socketsIds from the same user in an object
        if (!usersSocketsIds[user.id]) {
            usersSocketsIds[user.id] = [];
        }
        usersSocketsIds[user.id].push(socket.id);

        // user join room
        if (!usersOnARoom[room]) {
            usersOnARoom[room] = {};
        }
        if (!usersOnARoom[room][socket.id]) {
            usersOnARoom[room][socket.id] = user.id;
        }

        // se a socket não estiver no canal, faz join e envia para o canal quem está nele
        if (!socket.rooms.has(room)) {
            socket.join(room);

            if (!usersJoinOnARoom[room]) {
                usersJoinOnARoom[room] = {};
            }
            usersJoinOnARoom[room][user.id] = Date.now();

            io.in(room).emit('CONCURRENT', { room: room, users: getUsersOnContent(usersOnARoom[room], room) } );
        }
    });

    socket.on('LOCATION', function(data) {
        var user = data.user;
        var room = data.pathname;

        // remove user from the old channel and join to the new one (1 socket.id = 1 channel)
        for(var _room in usersOnARoom) {
            for(var _socketId in usersOnARoom[_room]) {
                if (_socketId == socket.id) {
                    delete usersOnARoom[_room][socket.id];
                    if (socket.rooms.has(_room)) {
                        socket.leave(_room);
                    }
                }
            }
        }

        // join the new channel
        if (!usersOnARoom[room]) {
            usersOnARoom[room] = {};
        }
        if (!usersOnARoom[room][socket.id]) {
            usersOnARoom[room][socket.id] = user.id;
        }

        // se a socket não estiver no canal, faz join e envia para o canal quem está nele
        if (!socket.rooms.has(room)) {
            socket.join(room);

            if (!usersJoinOnARoom[room]) {
                usersJoinOnARoom[room] = {};
            }
            usersJoinOnARoom[room][user.id] = Date.now();

            io.in(room).emit('CONCURRENT', { room: room, users: getUsersOnContent(usersOnARoom[room], room) } );
        }

        // enviar para todos os canais os users que estão nele e apagar canal se não tiver users
        for(var _room in usersOnARoom) {
            io.in(_room).emit('CONCURRENT', { room: _room, users: getUsersOnContent(usersOnARoom[_room], _room) } );

            if (Object.keys(usersOnARoom[_room]).length == 0) {
                delete usersOnARoom[_room];
            }
        }
    });

    socket.on('disconnect', function() {
        // remove the disconnected socket id from the array usersSocketsIds[user.id]
        for(var userId in usersSocketsIds) {
            for(var e = 0; e < usersSocketsIds[userId].length; e++) {
                if (socket.id == usersSocketsIds[userId][e]) {
                    usersSocketsIds[userId] = _.filter(usersSocketsIds[userId], function(s) { return s !== socket.id; });
                }
            }

            if (usersSocketsIds[userId].length == 0) {
                delete usersSocketsIds[userId];
            }
        }

        // delete this socket id from the usersOnARoom
        for(var _room in usersOnARoom) {
            for(var _socketId in usersOnARoom[_room]) {
                if (_socketId == socket.id) {
                    delete usersOnARoom[_room][_socketId];
                    if (socket.rooms.has(_room)) {
                        socket.leave(_room);
                    }
                }
            }

            // envia lista de users actualizada para o canal
            io.in(_room).emit('CONCURRENT', { room: _room, users: getUsersOnContent(usersOnARoom[_room], _room) });

            // se o canal não tiver ninguem, apaga
            if (Object.keys(usersOnARoom[_room]).length == 0) {
                delete usersOnARoom[_room];
            }
        }
    });
});
